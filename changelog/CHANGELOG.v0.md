<a name="readme-top"></a>

# Changelog

## [Version 0.163.0](https://github.com/lobehub/lobe-chat/compare/v0.162.25...v0.163.0)

<sup>Released on **2024-06-17**</sup>

#### ✨ Features

- **misc**: Support server db mode with Postgres / Drizzle ORM / tRPC.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support server db mode with Postgres / Drizzle ORM / tRPC, closes [#2556](https://github.com/lobehub/lobe-chat/issues/2556) ([b26afbf](https://github.com/lobehub/lobe-chat/commit/b26afbf))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.25](https://github.com/lobehub/lobe-chat/compare/v0.162.24...v0.162.25)

<sup>Released on **2024-06-16**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix issues for client fetch.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix issues for client fetch, closes [#2753](https://github.com/lobehub/lobe-chat/issues/2753) ([6f5be5d](https://github.com/lobehub/lobe-chat/commit/6f5be5d))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.24](https://github.com/lobehub/lobe-chat/compare/v0.162.23...v0.162.24)

<sup>Released on **2024-06-14**</sup>

#### 💄 Styles

- **misc**: Update error card style, Update settings footer style and about page.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update error card style, closes [#2868](https://github.com/lobehub/lobe-chat/issues/2868) ([a9d678b](https://github.com/lobehub/lobe-chat/commit/a9d678b))
- **misc**: Update settings footer style and about page, closes [#2846](https://github.com/lobehub/lobe-chat/issues/2846) ([d815109](https://github.com/lobehub/lobe-chat/commit/d815109))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.23](https://github.com/lobehub/lobe-chat/compare/v0.162.22...v0.162.23)

<sup>Released on **2024-06-12**</sup>

#### 💄 Styles

- **misc**: Add Qwen2 models, Add Zhipu new models.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add Qwen2 models, closes [#2832](https://github.com/lobehub/lobe-chat/issues/2832) ([fb97be9](https://github.com/lobehub/lobe-chat/commit/fb97be9))
- **misc**: Add Zhipu new models, closes [#2830](https://github.com/lobehub/lobe-chat/issues/2830) ([5be43f0](https://github.com/lobehub/lobe-chat/commit/5be43f0))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.22](https://github.com/lobehub/lobe-chat/compare/v0.162.21...v0.162.22)

<sup>Released on **2024-06-11**</sup>

#### 💄 Styles

- **misc**: Expand `Clear` tooltip maxWidth.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Expand `Clear` tooltip maxWidth, closes [#2822](https://github.com/lobehub/lobe-chat/issues/2822) ([a5c3d6f](https://github.com/lobehub/lobe-chat/commit/a5c3d6f))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.21](https://github.com/lobehub/lobe-chat/compare/v0.162.20...v0.162.21)

<sup>Released on **2024-06-09**</sup>

#### 💄 Styles

- **misc**: Do not show noDescription in new sesstion.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Do not show noDescription in new sesstion, closes [#2749](https://github.com/lobehub/lobe-chat/issues/2749) ([30b00aa](https://github.com/lobehub/lobe-chat/commit/30b00aa))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.20](https://github.com/lobehub/lobe-chat/compare/v0.162.19...v0.162.20)

<sup>Released on **2024-06-08**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.19](https://github.com/lobehub/lobe-chat/compare/v0.162.18...v0.162.19)

<sup>Released on **2024-06-07**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix OpenAi BaseURL in api form.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix OpenAi BaseURL in api form, closes [#2806](https://github.com/lobehub/lobe-chat/issues/2806) ([1392957](https://github.com/lobehub/lobe-chat/commit/1392957))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.18](https://github.com/lobehub/lobe-chat/compare/v0.162.17...v0.162.18)

<sup>Released on **2024-06-06**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor model provider implement.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor model provider implement, closes [#2801](https://github.com/lobehub/lobe-chat/issues/2801) ([7bb4fec](https://github.com/lobehub/lobe-chat/commit/7bb4fec))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.17](https://github.com/lobehub/lobe-chat/compare/v0.162.16...v0.162.17)

<sup>Released on **2024-06-04**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix `response.undefined` error with some provider.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix `response.undefined` error with some provider, closes [#2782](https://github.com/lobehub/lobe-chat/issues/2782) ([5676899](https://github.com/lobehub/lobe-chat/commit/5676899))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.16](https://github.com/lobehub/lobe-chat/compare/v0.162.15...v0.162.16)

<sup>Released on **2024-06-04**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.15](https://github.com/lobehub/lobe-chat/compare/v0.162.14...v0.162.15)

<sup>Released on **2024-06-03**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix send button loading on only add user message.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix send button loading on only add user message, closes [#2774](https://github.com/lobehub/lobe-chat/issues/2774) ([a7f2982](https://github.com/lobehub/lobe-chat/commit/a7f2982))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.14](https://github.com/lobehub/lobe-chat/compare/v0.162.13...v0.162.14)

<sup>Released on **2024-06-03**</sup>

#### 💄 Styles

- **misc**: Improve loading state.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve loading state, closes [#2767](https://github.com/lobehub/lobe-chat/issues/2767) ([fbdfde9](https://github.com/lobehub/lobe-chat/commit/fbdfde9))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.13](https://github.com/lobehub/lobe-chat/compare/v0.162.12...v0.162.13)

<sup>Released on **2024-06-01**</sup>

#### 💄 Styles

- **misc**: Improve config upload modal.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve config upload modal, closes [#2745](https://github.com/lobehub/lobe-chat/issues/2745) ([af9af9f](https://github.com/lobehub/lobe-chat/commit/af9af9f))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.12](https://github.com/lobehub/lobe-chat/compare/v0.162.11...v0.162.12)

<sup>Released on **2024-05-31**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor session meta method.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor session meta method, closes [#2737](https://github.com/lobehub/lobe-chat/issues/2737) ([b103c3c](https://github.com/lobehub/lobe-chat/commit/b103c3c))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.11](https://github.com/lobehub/lobe-chat/compare/v0.162.10...v0.162.11)

<sup>Released on **2024-05-29**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix import config.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix import config, closes [#2720](https://github.com/lobehub/lobe-chat/issues/2720) ([a5ddd9a](https://github.com/lobehub/lobe-chat/commit/a5ddd9a))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.10](https://github.com/lobehub/lobe-chat/compare/v0.162.9...v0.162.10)

<sup>Released on **2024-05-29**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the config import for server import.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the config import for server import, closes [#2718](https://github.com/lobehub/lobe-chat/issues/2718) ([d4ee64b](https://github.com/lobehub/lobe-chat/commit/d4ee64b))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.9](https://github.com/lobehub/lobe-chat/compare/v0.162.8...v0.162.9)

<sup>Released on **2024-05-29**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the settings to add optimistic updating.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the settings to add optimistic updating, closes [#2709](https://github.com/lobehub/lobe-chat/issues/2709) ([fade53e](https://github.com/lobehub/lobe-chat/commit/fade53e))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.8](https://github.com/lobehub/lobe-chat/compare/v0.162.7...v0.162.8)

<sup>Released on **2024-05-28**</sup>

#### 💄 Styles

- **misc**: Add optimistic loading for image uploading.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add optimistic loading for image uploading, closes [#2700](https://github.com/lobehub/lobe-chat/issues/2700) ([f99c9ce](https://github.com/lobehub/lobe-chat/commit/f99c9ce))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.7](https://github.com/lobehub/lobe-chat/compare/v0.162.6...v0.162.7)

<sup>Released on **2024-05-28**</sup>

#### 💄 Styles

- **misc**: Improve display of `set limited history messages`, `randomness` and `voice input`.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve display of `set limited history messages`, `randomness` and `voice input`, closes [#2586](https://github.com/lobehub/lobe-chat/issues/2586) ([22c9b9c](https://github.com/lobehub/lobe-chat/commit/22c9b9c))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.6](https://github.com/lobehub/lobe-chat/compare/v0.162.5...v0.162.6)

<sup>Released on **2024-05-28**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix the default agent not work correctly on new device.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix the default agent not work correctly on new device, closes [#2699](https://github.com/lobehub/lobe-chat/issues/2699) ([e4c7536](https://github.com/lobehub/lobe-chat/commit/e4c7536))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.5](https://github.com/lobehub/lobe-chat/compare/v0.162.4...v0.162.5)

<sup>Released on **2024-05-28**</sup>

#### 💄 Styles

- **misc**: Add `SYSTEM_AGENT` env.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add `SYSTEM_AGENT` env, closes [#2694](https://github.com/lobehub/lobe-chat/issues/2694) ([0dfcf8d](https://github.com/lobehub/lobe-chat/commit/0dfcf8d))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.4](https://github.com/lobehub/lobe-chat/compare/v0.162.3...v0.162.4)

<sup>Released on **2024-05-28**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix auto focus issues.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix auto focus issues, closes [#2697](https://github.com/lobehub/lobe-chat/issues/2697) ([8df856e](https://github.com/lobehub/lobe-chat/commit/8df856e))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.3](https://github.com/lobehub/lobe-chat/compare/v0.162.2...v0.162.3)

<sup>Released on **2024-05-28**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.2](https://github.com/lobehub/lobe-chat/compare/v0.162.1...v0.162.2)

<sup>Released on **2024-05-28**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor agent store data.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor agent store data, closes [#2690](https://github.com/lobehub/lobe-chat/issues/2690) ([e201937](https://github.com/lobehub/lobe-chat/commit/e201937))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.162.1](https://github.com/lobehub/lobe-chat/compare/v0.162.0...v0.162.1)

<sup>Released on **2024-05-27**</sup>

#### 💄 Styles

- **misc**: Improve the display effect of plug-in API name and description.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve the display effect of plug-in API name and description, closes [#2678](https://github.com/lobehub/lobe-chat/issues/2678) ([19cd0b9](https://github.com/lobehub/lobe-chat/commit/19cd0b9))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.162.0](https://github.com/lobehub/lobe-chat/compare/v0.161.25...v0.162.0)

<sup>Released on **2024-05-27**</sup>

#### ✨ Features

- **misc**: Support topic agent.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support topic agent, closes [#2683](https://github.com/lobehub/lobe-chat/issues/2683) ([56865fe](https://github.com/lobehub/lobe-chat/commit/56865fe))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.25](https://github.com/lobehub/lobe-chat/compare/v0.161.24...v0.161.25)

<sup>Released on **2024-05-27**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix trpc/edge path error when setting `NEXT_PUBLIC_BASE_PATH`.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix trpc/edge path error when setting `NEXT_PUBLIC_BASE_PATH`, closes [#2681](https://github.com/lobehub/lobe-chat/issues/2681) ([622b390](https://github.com/lobehub/lobe-chat/commit/622b390))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.24](https://github.com/lobehub/lobe-chat/compare/v0.161.23...v0.161.24)

<sup>Released on **2024-05-27**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix the missing user id in chat compeletition and fix remove unstarred topic not working.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix the missing user id in chat compeletition and fix remove unstarred topic not working, closes [#2677](https://github.com/lobehub/lobe-chat/issues/2677) ([c9fb2de](https://github.com/lobehub/lobe-chat/commit/c9fb2de))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.23](https://github.com/lobehub/lobe-chat/compare/v0.161.22...v0.161.23)

<sup>Released on **2024-05-27**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor `keyVaults` and rename `endpoint` to `baseURL`.

#### 💄 Styles

- **misc**: Fix PluginStore layout.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor `keyVaults` and rename `endpoint` to `baseURL`, closes [#2673](https://github.com/lobehub/lobe-chat/issues/2673) ([4049bc7](https://github.com/lobehub/lobe-chat/commit/4049bc7))

#### Styles

- **misc**: Fix PluginStore layout, closes [#2590](https://github.com/lobehub/lobe-chat/issues/2590) [#2511](https://github.com/lobehub/lobe-chat/issues/2511) ([e1ae39b](https://github.com/lobehub/lobe-chat/commit/e1ae39b))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.22](https://github.com/lobehub/lobe-chat/compare/v0.161.21...v0.161.22)

<sup>Released on **2024-05-26**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix connection checker.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix connection checker, closes [#2672](https://github.com/lobehub/lobe-chat/issues/2672) ([bef8926](https://github.com/lobehub/lobe-chat/commit/bef8926))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.21](https://github.com/lobehub/lobe-chat/compare/v0.161.20...v0.161.21)

<sup>Released on **2024-05-26**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix the batch import error.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix the batch import error, closes [#2671](https://github.com/lobehub/lobe-chat/issues/2671) ([db8f2c6](https://github.com/lobehub/lobe-chat/commit/db8f2c6))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.20](https://github.com/lobehub/lobe-chat/compare/v0.161.19...v0.161.20)

<sup>Released on **2024-05-26**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix vercel build.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix vercel build, closes [#2666](https://github.com/lobehub/lobe-chat/issues/2666) ([cb70e4a](https://github.com/lobehub/lobe-chat/commit/cb70e4a))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.19](https://github.com/lobehub/lobe-chat/compare/v0.161.18...v0.161.19)

<sup>Released on **2024-05-25**</sup>

#### 💄 Styles

- **misc**: Update token tag popover style.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update token tag popover style, closes [#2631](https://github.com/lobehub/lobe-chat/issues/2631) ([7635129](https://github.com/lobehub/lobe-chat/commit/7635129))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.18](https://github.com/lobehub/lobe-chat/compare/v0.161.17...v0.161.18)

<sup>Released on **2024-05-25**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix aws log.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix aws log ([58f3ed1](https://github.com/lobehub/lobe-chat/commit/58f3ed1))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.17](https://github.com/lobehub/lobe-chat/compare/v0.161.16...v0.161.17)

<sup>Released on **2024-05-25**</sup>

#### ♻ Code Refactoring

- **misc**: Migrate some agent config to `chatConfig`.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Migrate some agent config to `chatConfig`, closes [#2646](https://github.com/lobehub/lobe-chat/issues/2646) ([2f311dc](https://github.com/lobehub/lobe-chat/commit/2f311dc))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.16](https://github.com/lobehub/lobe-chat/compare/v0.161.15...v0.161.16)

<sup>Released on **2024-05-25**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix bedrock show by default on vercel.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix bedrock show by default on vercel, closes [#2634](https://github.com/lobehub/lobe-chat/issues/2634) ([7ad3af2](https://github.com/lobehub/lobe-chat/commit/7ad3af2))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.15](https://github.com/lobehub/lobe-chat/compare/v0.161.14...v0.161.15)

<sup>Released on **2024-05-24**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix avatar missing on client DB mode.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix avatar missing on client DB mode, closes [#2645](https://github.com/lobehub/lobe-chat/issues/2645) ([12726c2](https://github.com/lobehub/lobe-chat/commit/12726c2))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.14](https://github.com/lobehub/lobe-chat/compare/v0.161.13...v0.161.14)

<sup>Released on **2024-05-24**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the global app status and fix PWA installer.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the global app status and fix PWA installer, closes [#2637](https://github.com/lobehub/lobe-chat/issues/2637) ([1f70305](https://github.com/lobehub/lobe-chat/commit/1f70305))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.13](https://github.com/lobehub/lobe-chat/compare/v0.161.12...v0.161.13)

<sup>Released on **2024-05-24**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.12](https://github.com/lobehub/lobe-chat/compare/v0.161.11...v0.161.12)

<sup>Released on **2024-05-23**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the home redirect implement.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the home redirect implement, closes [#2626](https://github.com/lobehub/lobe-chat/issues/2626) ([ab4216e](https://github.com/lobehub/lobe-chat/commit/ab4216e))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.11](https://github.com/lobehub/lobe-chat/compare/v0.161.10...v0.161.11)

<sup>Released on **2024-05-23**</sup>

#### 💄 Styles

- **misc**: Improve PWA install guide.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve PWA install guide, closes [#2617](https://github.com/lobehub/lobe-chat/issues/2617) ([7fee545](https://github.com/lobehub/lobe-chat/commit/7fee545))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.10](https://github.com/lobehub/lobe-chat/compare/v0.161.9...v0.161.10)

<sup>Released on **2024-05-23**</sup>

#### 🐛 Bug Fixes

- **misc**: Refactor user store and fix custom model list form.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Refactor user store and fix custom model list form, closes [#2620](https://github.com/lobehub/lobe-chat/issues/2620) ([81ea886](https://github.com/lobehub/lobe-chat/commit/81ea886))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.9](https://github.com/lobehub/lobe-chat/compare/v0.161.8...v0.161.9)

<sup>Released on **2024-05-23**</sup>

#### 💄 Styles

- **misc**: Fix image style and improve drag upload box.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix image style and improve drag upload box, closes [#2610](https://github.com/lobehub/lobe-chat/issues/2610) ([5e1a4d6](https://github.com/lobehub/lobe-chat/commit/5e1a4d6))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.8](https://github.com/lobehub/lobe-chat/compare/v0.161.7...v0.161.8)

<sup>Released on **2024-05-22**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.7](https://github.com/lobehub/lobe-chat/compare/v0.161.6...v0.161.7)

<sup>Released on **2024-05-22**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor to serverDB ENV.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor to serverDB ENV, closes [#2612](https://github.com/lobehub/lobe-chat/issues/2612) ([fa1409e](https://github.com/lobehub/lobe-chat/commit/fa1409e))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.6](https://github.com/lobehub/lobe-chat/compare/v0.161.5...v0.161.6)

<sup>Released on **2024-05-22**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.5](https://github.com/lobehub/lobe-chat/compare/v0.161.4...v0.161.5)

<sup>Released on **2024-05-22**</sup>

#### ♻ Code Refactoring

- **misc**: Move feature flags ENV.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Move feature flags ENV, closes [#2605](https://github.com/lobehub/lobe-chat/issues/2605) ([054a404](https://github.com/lobehub/lobe-chat/commit/054a404))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.4](https://github.com/lobehub/lobe-chat/compare/v0.161.3...v0.161.4)

<sup>Released on **2024-05-22**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the app ENV.

#### 🐛 Bug Fixes

- **misc**: Fix market and plugin cache.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the app ENV, closes [#2604](https://github.com/lobehub/lobe-chat/issues/2604) ([acc0fdc](https://github.com/lobehub/lobe-chat/commit/acc0fdc))

#### What's fixed

- **misc**: Fix market and plugin cache, closes [#2608](https://github.com/lobehub/lobe-chat/issues/2608) ([a3f161e](https://github.com/lobehub/lobe-chat/commit/a3f161e))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.3](https://github.com/lobehub/lobe-chat/compare/v0.161.2...v0.161.3)

<sup>Released on **2024-05-22**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the langfuse env.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the langfuse env, closes [#2602](https://github.com/lobehub/lobe-chat/issues/2602) ([cbebfbc](https://github.com/lobehub/lobe-chat/commit/cbebfbc))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.2](https://github.com/lobehub/lobe-chat/compare/v0.161.1...v0.161.2)

<sup>Released on **2024-05-22**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the llm env.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the llm env, closes [#2592](https://github.com/lobehub/lobe-chat/issues/2592) ([5eb225a](https://github.com/lobehub/lobe-chat/commit/5eb225a))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.161.1](https://github.com/lobehub/lobe-chat/compare/v0.161.0...v0.161.1)

<sup>Released on **2024-05-22**</sup>

#### 💄 Styles

- **misc**: Fix setting modal style problem.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix setting modal style problem, closes [#2599](https://github.com/lobehub/lobe-chat/issues/2599) ([1a3f8f3](https://github.com/lobehub/lobe-chat/commit/1a3f8f3))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.161.0](https://github.com/lobehub/lobe-chat/compare/v0.160.8...v0.161.0)

<sup>Released on **2024-05-21**</sup>

#### ✨ Features

- **misc**: Add system agent to select another model provider for translation.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add system agent to select another model provider for translation, closes [#1902](https://github.com/lobehub/lobe-chat/issues/1902) ([3945387](https://github.com/lobehub/lobe-chat/commit/3945387))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.160.8](https://github.com/lobehub/lobe-chat/compare/v0.160.7...v0.160.8)

<sup>Released on **2024-05-21**</sup>

#### 💄 Styles

- **misc**: `Tooltip` should not be selected & Model selector overlaps with reset button.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: `Tooltip` should not be selected & Model selector overlaps with reset button, closes [#2500](https://github.com/lobehub/lobe-chat/issues/2500) [#2511](https://github.com/lobehub/lobe-chat/issues/2511) [#2581](https://github.com/lobehub/lobe-chat/issues/2581) ([0c62fb7](https://github.com/lobehub/lobe-chat/commit/0c62fb7))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.160.7](https://github.com/lobehub/lobe-chat/compare/v0.160.6...v0.160.7)

<sup>Released on **2024-05-21**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix duplicate model panel key.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix duplicate model panel key, closes [#2591](https://github.com/lobehub/lobe-chat/issues/2591) ([c733fcf](https://github.com/lobehub/lobe-chat/commit/c733fcf))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.160.6](https://github.com/lobehub/lobe-chat/compare/v0.160.5...v0.160.6)

<sup>Released on **2024-05-21**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor a Notification component, refactor code, refactor message action, refactor the type, refactor user preference.

#### 💄 Styles

- **misc**: Add ENABLED_OPENAI env, add fetch error notification, improve openai fetch client switch, improve redirect when login.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor a Notification component ([28db3d5](https://github.com/lobehub/lobe-chat/commit/28db3d5))
- **misc**: Refactor code, closes [#2584](https://github.com/lobehub/lobe-chat/issues/2584) ([086244c](https://github.com/lobehub/lobe-chat/commit/086244c))
- **misc**: Refactor message action ([224bd67](https://github.com/lobehub/lobe-chat/commit/224bd67))
- **misc**: Refactor the type ([ddf1abf](https://github.com/lobehub/lobe-chat/commit/ddf1abf))
- **misc**: Refactor user preference ([1823b0d](https://github.com/lobehub/lobe-chat/commit/1823b0d))

#### Styles

- **misc**: Add ENABLED_OPENAI env ([35f6230](https://github.com/lobehub/lobe-chat/commit/35f6230))
- **misc**: Add fetch error notification ([0186b4b](https://github.com/lobehub/lobe-chat/commit/0186b4b))
- **misc**: Improve openai fetch client switch ([3cad470](https://github.com/lobehub/lobe-chat/commit/3cad470))
- **misc**: Improve redirect when login ([cb26655](https://github.com/lobehub/lobe-chat/commit/cb26655))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.160.5](https://github.com/lobehub/lobe-chat/compare/v0.160.4...v0.160.5)

<sup>Released on **2024-05-20**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor analytics env.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor analytics env, closes [#2570](https://github.com/lobehub/lobe-chat/issues/2570) ([d809d3f](https://github.com/lobehub/lobe-chat/commit/d809d3f))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.160.4](https://github.com/lobehub/lobe-chat/compare/v0.160.3...v0.160.4)

<sup>Released on **2024-05-20**</sup>

#### 💄 Styles

- **misc**: Modify bedrock provided model.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Modify bedrock provided model, closes [#2473](https://github.com/lobehub/lobe-chat/issues/2473) ([a1fabf6](https://github.com/lobehub/lobe-chat/commit/a1fabf6))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.160.3](https://github.com/lobehub/lobe-chat/compare/v0.160.2...v0.160.3)

<sup>Released on **2024-05-19**</sup>

#### 💄 Styles

- **misc**: Add two feature flags: check_updates 、welcome_suggest.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add two feature flags: check_updates 、welcome_suggest, closes [#2555](https://github.com/lobehub/lobe-chat/issues/2555) ([84c69c9](https://github.com/lobehub/lobe-chat/commit/84c69c9))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.160.2](https://github.com/lobehub/lobe-chat/compare/v0.160.1...v0.160.2)

<sup>Released on **2024-05-19**</sup>

#### 🐛 Bug Fixes

- **misc**: Upgrade antd and fix lint type.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Upgrade antd and fix lint type, closes [#2567](https://github.com/lobehub/lobe-chat/issues/2567) ([efe28da](https://github.com/lobehub/lobe-chat/commit/efe28da))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.160.1](https://github.com/lobehub/lobe-chat/compare/v0.160.0...v0.160.1)

<sup>Released on **2024-05-18**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix enable ollama env.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix enable ollama env ([9c3f5a8](https://github.com/lobehub/lobe-chat/commit/9c3f5a8))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.160.0](https://github.com/lobehub/lobe-chat/compare/v0.159.12...v0.160.0)

<sup>Released on **2024-05-18**</sup>

#### ✨ Features

- **misc**: Bump version and add enable ollama env.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Bump version and add enable ollama env, closes [#2554](https://github.com/lobehub/lobe-chat/issues/2554) ([f5ce7c9](https://github.com/lobehub/lobe-chat/commit/f5ce7c9))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.159.12](https://github.com/lobehub/lobe-chat/compare/v0.159.11...v0.159.12)

<sup>Released on **2024-05-15**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the create message flow to fix some bugs.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the create message flow to fix some bugs, closes [#2521](https://github.com/lobehub/lobe-chat/issues/2521) ([7263a33](https://github.com/lobehub/lobe-chat/commit/7263a33))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.159.11](https://github.com/lobehub/lobe-chat/compare/v0.159.10...v0.159.11)

<sup>Released on **2024-05-15**</sup>

#### 💄 Styles

- **misc**: Add Gemini 1.5 Flash model.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add Gemini 1.5 Flash model, closes [#2507](https://github.com/lobehub/lobe-chat/issues/2507) ([5568472](https://github.com/lobehub/lobe-chat/commit/5568472))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.159.10](https://github.com/lobehub/lobe-chat/compare/v0.159.9...v0.159.10)

<sup>Released on **2024-05-15**</sup>

#### 💄 Styles

- **misc**: Fix setting modal on responsive and some other style problem.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix setting modal on responsive and some other style problem, closes [#2512](https://github.com/lobehub/lobe-chat/issues/2512) ([f6b4ca4](https://github.com/lobehub/lobe-chat/commit/f6b4ca4))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.159.9](https://github.com/lobehub/lobe-chat/compare/v0.159.8...v0.159.9)

<sup>Released on **2024-05-14**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix agent config on page init.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix agent config on page init, closes [#2506](https://github.com/lobehub/lobe-chat/issues/2506) ([90e742d](https://github.com/lobehub/lobe-chat/commit/90e742d))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.159.8](https://github.com/lobehub/lobe-chat/compare/v0.159.7...v0.159.8)

<sup>Released on **2024-05-14**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix retry issue when hide page.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix retry issue when hide page, closes [#2503](https://github.com/lobehub/lobe-chat/issues/2503) ([24489bc](https://github.com/lobehub/lobe-chat/commit/24489bc))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.159.7](https://github.com/lobehub/lobe-chat/compare/v0.159.6...v0.159.7)

<sup>Released on **2024-05-14**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.159.6](https://github.com/lobehub/lobe-chat/compare/v0.159.5...v0.159.6)

<sup>Released on **2024-05-14**</sup>

#### 🐛 Bug Fixes

- **misc**: Login button not show on user panel.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Login button not show on user panel, closes [#2496](https://github.com/lobehub/lobe-chat/issues/2496) ([39637fb](https://github.com/lobehub/lobe-chat/commit/39637fb))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.159.5](https://github.com/lobehub/lobe-chat/compare/v0.159.4...v0.159.5)

<sup>Released on **2024-05-14**</sup>

#### 💄 Styles

- **misc**: Fix scroll and expand.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix scroll and expand, closes [#2470](https://github.com/lobehub/lobe-chat/issues/2470) ([8b1202a](https://github.com/lobehub/lobe-chat/commit/8b1202a))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.159.4](https://github.com/lobehub/lobe-chat/compare/v0.159.3...v0.159.4)

<sup>Released on **2024-05-14**</sup>

#### 🐛 Bug Fixes

- **misc**: Refresh model config form & mobile footer button lost.

#### 💄 Styles

- **misc**: Add GPT-4o model, update perplexity models, updates 01.AI model list.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Refresh model config form & mobile footer button lost, closes [#2318](https://github.com/lobehub/lobe-chat/issues/2318) [#2319](https://github.com/lobehub/lobe-chat/issues/2319) [#1811](https://github.com/lobehub/lobe-chat/issues/1811) ([eadcefc](https://github.com/lobehub/lobe-chat/commit/eadcefc))

#### Styles

- **misc**: Add GPT-4o model, closes [#2481](https://github.com/lobehub/lobe-chat/issues/2481) ([ae6a03f](https://github.com/lobehub/lobe-chat/commit/ae6a03f))
- **misc**: Update perplexity models, closes [#2469](https://github.com/lobehub/lobe-chat/issues/2469) ([488cde7](https://github.com/lobehub/lobe-chat/commit/488cde7))
- **misc**: Updates 01.AI model list, closes [#2471](https://github.com/lobehub/lobe-chat/issues/2471) ([f28711a](https://github.com/lobehub/lobe-chat/commit/f28711a))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.159.3](https://github.com/lobehub/lobe-chat/compare/v0.159.2...v0.159.3)

<sup>Released on **2024-05-14**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix DeepSeek using wrong model ID.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix DeepSeek using wrong model ID, closes [#2484](https://github.com/lobehub/lobe-chat/issues/2484) ([465dbfc](https://github.com/lobehub/lobe-chat/commit/465dbfc))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.159.2](https://github.com/lobehub/lobe-chat/compare/v0.159.1...v0.159.2)

<sup>Released on **2024-05-14**</sup>

#### 🐛 Bug Fixes

- **misc**: Dragging text mistakenly as image.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Dragging text mistakenly as image, closes [#2111](https://github.com/lobehub/lobe-chat/issues/2111) ([3c047ef](https://github.com/lobehub/lobe-chat/commit/3c047ef))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.159.1](https://github.com/lobehub/lobe-chat/compare/v0.159.0...v0.159.1)

<sup>Released on **2024-05-14**</sup>

#### ♻ Code Refactoring

- **misc**: Move next-auth hooks to user store actions.

#### 🐛 Bug Fixes

- **misc**: Pin `antd@5.17.0` to fix build error.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Move next-auth hooks to user store actions, closes [#2364](https://github.com/lobehub/lobe-chat/issues/2364) ([6dbcd70](https://github.com/lobehub/lobe-chat/commit/6dbcd70))

#### What's fixed

- **misc**: Pin `antd@5.17.0` to fix build error, closes [#2483](https://github.com/lobehub/lobe-chat/issues/2483) ([aa03833](https://github.com/lobehub/lobe-chat/commit/aa03833))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.159.0](https://github.com/lobehub/lobe-chat/compare/v0.158.2...v0.159.0)

<sup>Released on **2024-05-14**</sup>

#### ✨ Features

- **misc**: Support DeepSeek as new model provider.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support DeepSeek as new model provider, closes [#2446](https://github.com/lobehub/lobe-chat/issues/2446) ([18028f3](https://github.com/lobehub/lobe-chat/commit/18028f3))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.158.2](https://github.com/lobehub/lobe-chat/compare/v0.158.1...v0.158.2)

<sup>Released on **2024-05-13**</sup>

#### 💄 Styles

- **misc**: Fix TelemetryNotification zindex.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix TelemetryNotification zindex, closes [#2476](https://github.com/lobehub/lobe-chat/issues/2476) ([54524ab](https://github.com/lobehub/lobe-chat/commit/54524ab))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.158.1](https://github.com/lobehub/lobe-chat/compare/v0.158.0...v0.158.1)

<sup>Released on **2024-05-13**</sup>

#### 💄 Styles

- **misc**: Add PWA install and metadata & ld generate.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add PWA install and metadata & ld generate, closes [#2438](https://github.com/lobehub/lobe-chat/issues/2438) ([6e9c69a](https://github.com/lobehub/lobe-chat/commit/6e9c69a))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.158.0](https://github.com/lobehub/lobe-chat/compare/v0.157.2...v0.158.0)

<sup>Released on **2024-05-13**</sup>

#### ✨ Features

- **misc**: Add user profile page.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add user profile page, closes [#2433](https://github.com/lobehub/lobe-chat/issues/2433) ([91f7294](https://github.com/lobehub/lobe-chat/commit/91f7294))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.157.2](https://github.com/lobehub/lobe-chat/compare/v0.157.1...v0.157.2)

<sup>Released on **2024-05-13**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix azure openai stream.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix azure openai stream, closes [#2465](https://github.com/lobehub/lobe-chat/issues/2465) ([760fe67](https://github.com/lobehub/lobe-chat/commit/760fe67))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.157.1](https://github.com/lobehub/lobe-chat/compare/v0.157.0...v0.157.1)

<sup>Released on **2024-05-12**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix dalle error.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix dalle error ([7c493de](https://github.com/lobehub/lobe-chat/commit/7c493de))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.157.0](https://github.com/lobehub/lobe-chat/compare/v0.156.2...v0.157.0)

<sup>Released on **2024-05-11**</sup>

#### ✨ Features

- **misc**: upgrade to the new `tool calls` mode.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: upgrade to the new `tool calls` mode, closes [#2414](https://github.com/lobehub/lobe-chat/issues/2414) ([7404f3b](https://github.com/lobehub/lobe-chat/commit/7404f3b))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.156.2](https://github.com/lobehub/lobe-chat/compare/v0.156.1...v0.156.2)

<sup>Released on **2024-05-10**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.156.1](https://github.com/lobehub/lobe-chat/compare/v0.156.0...v0.156.1)

<sup>Released on **2024-05-10**</sup>

#### 🐛 Bug Fixes

- **misc**: Azure OpenAI Vision models issue.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Azure OpenAI Vision models issue, closes [#2429](https://github.com/lobehub/lobe-chat/issues/2429) ([9b8a4b1](https://github.com/lobehub/lobe-chat/commit/9b8a4b1))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.156.0](https://github.com/lobehub/lobe-chat/compare/v0.155.9...v0.156.0)

<sup>Released on **2024-05-09**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor to improve provider locale.

#### ✨ Features

- **misc**: Support perplexity proxy url.

#### 🐛 Bug Fixes

- **misc**: Fix anthropic messages need pairs, fix parameter conditions for perplexity, fix Perplexity duplicate token.

#### 💄 Styles

- **misc**: Improve groq location error, improve location error, improve model provider ux, support groq proxy url.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor to improve provider locale ([538b7bc](https://github.com/lobehub/lobe-chat/commit/538b7bc))

#### What's improved

- **misc**: Support perplexity proxy url ([30bbe61](https://github.com/lobehub/lobe-chat/commit/30bbe61))

#### What's fixed

- **misc**: Fix anthropic messages need pairs ([0e01db0](https://github.com/lobehub/lobe-chat/commit/0e01db0))
- **misc**: Fix parameter conditions for perplexity ([156cf5f](https://github.com/lobehub/lobe-chat/commit/156cf5f))
- **misc**: Fix Perplexity duplicate token ([6695c4b](https://github.com/lobehub/lobe-chat/commit/6695c4b))

#### Styles

- **misc**: Improve groq location error ([023c21b](https://github.com/lobehub/lobe-chat/commit/023c21b))
- **misc**: Improve location error ([862c0ae](https://github.com/lobehub/lobe-chat/commit/862c0ae))
- **misc**: Improve model provider ux, closes [#2439](https://github.com/lobehub/lobe-chat/issues/2439) ([0deb079](https://github.com/lobehub/lobe-chat/commit/0deb079))
- **misc**: Support groq proxy url ([ba1ba2a](https://github.com/lobehub/lobe-chat/commit/ba1ba2a))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.155.9](https://github.com/lobehub/lobe-chat/compare/v0.155.8...v0.155.9)

<sup>Released on **2024-05-09**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.155.8](https://github.com/lobehub/lobe-chat/compare/v0.155.7...v0.155.8)

<sup>Released on **2024-05-09**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix mobile session style.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix mobile session style ([998a191](https://github.com/lobehub/lobe-chat/commit/998a191))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.155.7](https://github.com/lobehub/lobe-chat/compare/v0.155.6...v0.155.7)

<sup>Released on **2024-05-08**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix panel expand.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix panel expand ([5e78089](https://github.com/lobehub/lobe-chat/commit/5e78089))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.155.6](https://github.com/lobehub/lobe-chat/compare/v0.155.5...v0.155.6)

<sup>Released on **2024-05-08**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix editing long message issue.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix editing long message issue, closes [#2431](https://github.com/lobehub/lobe-chat/issues/2431) ([380d8da](https://github.com/lobehub/lobe-chat/commit/380d8da))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.155.5](https://github.com/lobehub/lobe-chat/compare/v0.155.4...v0.155.5)

<sup>Released on **2024-05-08**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.155.4](https://github.com/lobehub/lobe-chat/compare/v0.155.3...v0.155.4)

<sup>Released on **2024-05-08**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix Agent Settings Form.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix Agent Settings Form ([12c62a0](https://github.com/lobehub/lobe-chat/commit/12c62a0))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.155.3](https://github.com/lobehub/lobe-chat/compare/v0.155.2...v0.155.3)

<sup>Released on **2024-05-08**</sup>

#### 💄 Styles

- **misc**: Optimized MaxToken Slider.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Optimized MaxToken Slider, closes [#2258](https://github.com/lobehub/lobe-chat/issues/2258) ([dfb892b](https://github.com/lobehub/lobe-chat/commit/dfb892b))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.155.2](https://github.com/lobehub/lobe-chat/compare/v0.155.1...v0.155.2)

<sup>Released on **2024-05-08**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.155.1](https://github.com/lobehub/lobe-chat/compare/v0.155.0...v0.155.1)

<sup>Released on **2024-05-07**</sup>

#### 💄 Styles

- **misc**: Improve llm list when all closed.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve llm list when all closed, closes [#2409](https://github.com/lobehub/lobe-chat/issues/2409) ([1eb20c7](https://github.com/lobehub/lobe-chat/commit/1eb20c7))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.155.0](https://github.com/lobehub/lobe-chat/compare/v0.154.7...v0.155.0)

<sup>Released on **2024-05-07**</sup>

#### ✨ Features

- **misc**: Add DataStatistics.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add DataStatistics ([cf474bb](https://github.com/lobehub/lobe-chat/commit/cf474bb))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.154.7](https://github.com/lobehub/lobe-chat/compare/v0.154.6...v0.154.7)

<sup>Released on **2024-05-07**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the message slice internal method name.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the message slice internal method name, closes [#2407](https://github.com/lobehub/lobe-chat/issues/2407) ([8c70bdd](https://github.com/lobehub/lobe-chat/commit/8c70bdd))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.154.6](https://github.com/lobehub/lobe-chat/compare/v0.154.5...v0.154.6)

<sup>Released on **2024-05-07**</sup>

#### 💄 Styles

- **misc**: Add gemini-1.0-pro-002.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add gemini-1.0-pro-002, closes [#2406](https://github.com/lobehub/lobe-chat/issues/2406) ([44b29a9](https://github.com/lobehub/lobe-chat/commit/44b29a9))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.154.5](https://github.com/lobehub/lobe-chat/compare/v0.154.4...v0.154.5)

<sup>Released on **2024-05-06**</sup>

#### 💄 Styles

- **misc**: Update LLM Settings Form styles.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update LLM Settings Form styles, closes [#2393](https://github.com/lobehub/lobe-chat/issues/2393) ([4f98e6c](https://github.com/lobehub/lobe-chat/commit/4f98e6c))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.154.4](https://github.com/lobehub/lobe-chat/compare/v0.154.3...v0.154.4)

<sup>Released on **2024-05-06**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix next auth config.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix next auth config, closes [#2390](https://github.com/lobehub/lobe-chat/issues/2390) ([dbda107](https://github.com/lobehub/lobe-chat/commit/dbda107))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.154.3](https://github.com/lobehub/lobe-chat/compare/v0.154.2...v0.154.3)

<sup>Released on **2024-05-06**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix docker build.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix docker build ([80a270d](https://github.com/lobehub/lobe-chat/commit/80a270d))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.154.2](https://github.com/lobehub/lobe-chat/compare/v0.154.1...v0.154.2)

<sup>Released on **2024-05-06**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix docker build.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix docker build, closes [#2385](https://github.com/lobehub/lobe-chat/issues/2385) ([9cf60b5](https://github.com/lobehub/lobe-chat/commit/9cf60b5))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.154.1](https://github.com/lobehub/lobe-chat/compare/v0.154.0...v0.154.1)

<sup>Released on **2024-05-05**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix agent meta input disabled.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix agent meta input disabled, closes [#2381](https://github.com/lobehub/lobe-chat/issues/2381) ([d1fc512](https://github.com/lobehub/lobe-chat/commit/d1fc512))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.154.0](https://github.com/lobehub/lobe-chat/compare/v0.153.1...v0.154.0)

<sup>Released on **2024-05-05**</sup>

#### ✨ Features

- **misc**: Support clerk as auth provider.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support clerk as auth provider, closes [#2374](https://github.com/lobehub/lobe-chat/issues/2374) ([bf8ef1f](https://github.com/lobehub/lobe-chat/commit/bf8ef1f))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.153.1](https://github.com/lobehub/lobe-chat/compare/v0.153.0...v0.153.1)

<sup>Released on **2024-05-04**</sup>

#### 💄 Styles

- **misc**: Imporve mobile styles and loading skeleton.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Imporve mobile styles and loading skeleton, closes [#2363](https://github.com/lobehub/lobe-chat/issues/2363) ([8ee3591](https://github.com/lobehub/lobe-chat/commit/8ee3591))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.153.0](https://github.com/lobehub/lobe-chat/compare/v0.152.12...v0.153.0)

<sup>Released on **2024-05-04**</sup>

#### ✨ Features

- **misc**: Add Settings Intercepting Routes.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add Settings Intercepting Routes, closes [#2346](https://github.com/lobehub/lobe-chat/issues/2346) ([29b6442](https://github.com/lobehub/lobe-chat/commit/29b6442))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.152.12](https://github.com/lobehub/lobe-chat/compare/v0.152.11...v0.152.12)

<sup>Released on **2024-05-04**</sup>

#### 🐛 Bug Fixes

- **misc**: Disabled autogenerate field icon when empty system role.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Disabled autogenerate field icon when empty system role, closes [#2076](https://github.com/lobehub/lobe-chat/issues/2076) ([27095f5](https://github.com/lobehub/lobe-chat/commit/27095f5))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.152.11](https://github.com/lobehub/lobe-chat/compare/v0.152.10...v0.152.11)

<sup>Released on **2024-05-03**</sup>

#### 💄 Styles

- **misc**: Add user panel and refactor the next-auth.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add user panel and refactor the next-auth, closes [#2349](https://github.com/lobehub/lobe-chat/issues/2349) ([5cecee0](https://github.com/lobehub/lobe-chat/commit/5cecee0))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.152.10](https://github.com/lobehub/lobe-chat/compare/v0.152.9...v0.152.10)

<sup>Released on **2024-05-03**</sup>

#### 💄 Styles

- **misc**: Improve market layout styles and mobile style.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve market layout styles and mobile style, closes [#2347](https://github.com/lobehub/lobe-chat/issues/2347) ([79b8115](https://github.com/lobehub/lobe-chat/commit/79b8115))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.152.9](https://github.com/lobehub/lobe-chat/compare/v0.152.8...v0.152.9)

<sup>Released on **2024-05-03**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.152.8](https://github.com/lobehub/lobe-chat/compare/v0.152.7...v0.152.8)

<sup>Released on **2024-05-03**</sup>

#### ♻ Code Refactoring

- **misc**: User store add an auth slice.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: User store add an auth slice, closes [#2214](https://github.com/lobehub/lobe-chat/issues/2214) ([948b257](https://github.com/lobehub/lobe-chat/commit/948b257))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.152.7](https://github.com/lobehub/lobe-chat/compare/v0.152.6...v0.152.7)

<sup>Released on **2024-05-02**</sup>

#### 💄 Styles

- **misc**: Refactor setting layout and improve setting design.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Refactor setting layout and improve setting design, closes [#2344](https://github.com/lobehub/lobe-chat/issues/2344) ([fa16721](https://github.com/lobehub/lobe-chat/commit/fa16721))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.152.6](https://github.com/lobehub/lobe-chat/compare/v0.152.5...v0.152.6)

<sup>Released on **2024-05-02**</sup>

#### 💄 Styles

- **misc**: AutoScroll to the fully end.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: AutoScroll to the fully end, closes [#2345](https://github.com/lobehub/lobe-chat/issues/2345) ([5458e98](https://github.com/lobehub/lobe-chat/commit/5458e98))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.152.5](https://github.com/lobehub/lobe-chat/compare/v0.152.4...v0.152.5)

<sup>Released on **2024-05-02**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix Setings Layout.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix Setings Layout, closes [#2340](https://github.com/lobehub/lobe-chat/issues/2340) ([6a2e9a9](https://github.com/lobehub/lobe-chat/commit/6a2e9a9))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.152.1](https://github.com/lobehub/lobe-chat/compare/v0.152.0...v0.152.1)

<sup>Released on **2024-05-01**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor Market Layout.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor Market Layout, closes [#2320](https://github.com/lobehub/lobe-chat/issues/2320) ([c05c3c1](https://github.com/lobehub/lobe-chat/commit/c05c3c1))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.152.0](https://github.com/lobehub/lobe-chat/compare/v0.151.11...v0.152.0)

<sup>Released on **2024-04-30**</sup>

#### ✨ Features

- **misc**: Import settings from url.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Import settings from url, closes [#2226](https://github.com/lobehub/lobe-chat/issues/2226) ([b1f6c20](https://github.com/lobehub/lobe-chat/commit/b1f6c20))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.151.11](https://github.com/lobehub/lobe-chat/compare/v0.151.10...v0.151.11)

<sup>Released on **2024-04-30**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix telemetry preference modal and default agent config error.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix telemetry preference modal and default agent config error, closes [#2312](https://github.com/lobehub/lobe-chat/issues/2312) ([8900445](https://github.com/lobehub/lobe-chat/commit/8900445))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.151.10](https://github.com/lobehub/lobe-chat/compare/v0.151.9...v0.151.10)

<sup>Released on **2024-04-30**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor Welcome Layout.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor Welcome Layout, closes [#2314](https://github.com/lobehub/lobe-chat/issues/2314) ([1040051](https://github.com/lobehub/lobe-chat/commit/1040051))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.151.9](https://github.com/lobehub/lobe-chat/compare/v0.151.8...v0.151.9)

<sup>Released on **2024-04-30**</sup>

#### 🐛 Bug Fixes

- **misc**: Minimax truncationed output.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Minimax truncationed output, closes [#2308](https://github.com/lobehub/lobe-chat/issues/2308) ([488f319](https://github.com/lobehub/lobe-chat/commit/488f319))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.151.8](https://github.com/lobehub/lobe-chat/compare/v0.151.7...v0.151.8)

<sup>Released on **2024-04-30**</sup>

#### ♻ Code Refactoring

- **misc**: Move NavBar to `[@nav](https://github.com/nav)` slot route.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Move NavBar to `[@nav](https://github.com/nav)` slot route, closes [#2306](https://github.com/lobehub/lobe-chat/issues/2306) ([aee7231](https://github.com/lobehub/lobe-chat/commit/aee7231))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.151.7](https://github.com/lobehub/lobe-chat/compare/v0.151.6...v0.151.7)

<sup>Released on **2024-04-30**</sup>

#### 💄 Styles

- **misc**: Add 404 and Error page.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add 404 and Error page, closes [#2299](https://github.com/lobehub/lobe-chat/issues/2299) ([938a3e9](https://github.com/lobehub/lobe-chat/commit/938a3e9))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.151.6](https://github.com/lobehub/lobe-chat/compare/v0.151.5...v0.151.6)

<sup>Released on **2024-04-30**</sup>

#### 🐛 Bug Fixes

- **misc**: Plugins with multiple settings cannot be correctly configured.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Plugins with multiple settings cannot be correctly configured, closes [#1991](https://github.com/lobehub/lobe-chat/issues/1991) ([0c041aa](https://github.com/lobehub/lobe-chat/commit/0c041aa))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.151.5](https://github.com/lobehub/lobe-chat/compare/v0.151.4...v0.151.5)

<sup>Released on **2024-04-30**</sup>

#### 🐛 Bug Fixes

- **misc**: Effectively interrupt auto scrolling.

#### 💄 Styles

- **misc**: Revise some text.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Effectively interrupt auto scrolling, closes [#2223](https://github.com/lobehub/lobe-chat/issues/2223) ([afe4974](https://github.com/lobehub/lobe-chat/commit/afe4974))

#### Styles

- **misc**: Revise some text, closes [#2296](https://github.com/lobehub/lobe-chat/issues/2296) ([018427f](https://github.com/lobehub/lobe-chat/commit/018427f))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.151.4](https://github.com/lobehub/lobe-chat/compare/v0.151.3...v0.151.4)

<sup>Released on **2024-04-29**</sup>

#### ♻ Code Refactoring

- **misc**: Move app page to the `(main)` layout group.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Move app page to the `(main)` layout group, closes [#2297](https://github.com/lobehub/lobe-chat/issues/2297) ([6cc2c38](https://github.com/lobehub/lobe-chat/commit/6cc2c38))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.151.3](https://github.com/lobehub/lobe-chat/compare/v0.151.2...v0.151.3)

<sup>Released on **2024-04-29**</sup>

#### 💄 Styles

- **misc**: Patching models info.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Patching models info, closes [#2269](https://github.com/lobehub/lobe-chat/issues/2269) [#22802280](https://github.com/lobehub/lobe-chat/issues/22802280) ([03bcb06](https://github.com/lobehub/lobe-chat/commit/03bcb06))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.151.2](https://github.com/lobehub/lobe-chat/compare/v0.151.1...v0.151.2)

<sup>Released on **2024-04-29**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix only inject welcome question in inbox.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix only inject welcome question in inbox, closes [#2289](https://github.com/lobehub/lobe-chat/issues/2289) ([cc8edd3](https://github.com/lobehub/lobe-chat/commit/cc8edd3))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.151.1](https://github.com/lobehub/lobe-chat/compare/v0.151.0...v0.151.1)

<sup>Released on **2024-04-29**</sup>

#### 💄 Styles

- **misc**: Improve Inbox Assistant Welcome Guide.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve Inbox Assistant Welcome Guide, closes [#2086](https://github.com/lobehub/lobe-chat/issues/2086) ([df37212](https://github.com/lobehub/lobe-chat/commit/df37212))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.151.0](https://github.com/lobehub/lobe-chat/compare/v0.150.10...v0.151.0)

<sup>Released on **2024-04-29**</sup>

#### ✨ Features

- **misc**: Support minimax as a new provider.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support minimax as a new provider, closes [#2087](https://github.com/lobehub/lobe-chat/issues/2087) ([00abd82](https://github.com/lobehub/lobe-chat/commit/00abd82))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.150.10](https://github.com/lobehub/lobe-chat/compare/v0.150.9...v0.150.10)

<sup>Released on **2024-04-28**</sup>

#### ♻ Code Refactoring

- **misc**: Rename globalStore to userStore.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Rename globalStore to userStore, closes [#2264](https://github.com/lobehub/lobe-chat/issues/2264) ([a3cb538](https://github.com/lobehub/lobe-chat/commit/a3cb538))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.150.9](https://github.com/lobehub/lobe-chat/compare/v0.150.8...v0.150.9)

<sup>Released on **2024-04-28**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor feature flags store to server config store.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor feature flags store to server config store, closes [#2263](https://github.com/lobehub/lobe-chat/issues/2263) ([2e991d7](https://github.com/lobehub/lobe-chat/commit/2e991d7))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.150.8](https://github.com/lobehub/lobe-chat/compare/v0.150.7...v0.150.8)

<sup>Released on **2024-04-28**</sup>

#### 💄 Styles

- **ollama**: Phi3 Instruct models and its model icons.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **ollama**: Phi3 Instruct models and its model icons, closes [#2254](https://github.com/lobehub/lobe-chat/issues/2254) ([c9b55cc](https://github.com/lobehub/lobe-chat/commit/c9b55cc))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.150.7](https://github.com/lobehub/lobe-chat/compare/v0.150.6...v0.150.7)

<sup>Released on **2024-04-28**</sup>

#### 🐛 Bug Fixes

- **misc**: Suport to fetch model list on client.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Suport to fetch model list on client, closes [#2252](https://github.com/lobehub/lobe-chat/issues/2252) ([76310a8](https://github.com/lobehub/lobe-chat/commit/76310a8))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.150.6](https://github.com/lobehub/lobe-chat/compare/v0.150.5...v0.150.6)

<sup>Released on **2024-04-28**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix `/api/proxy` internal proxy attack.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix `/api/proxy` internal proxy attack, closes [#2255](https://github.com/lobehub/lobe-chat/issues/2255) ([465665a](https://github.com/lobehub/lobe-chat/commit/465665a))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.150.5](https://github.com/lobehub/lobe-chat/compare/v0.150.4...v0.150.5)

<sup>Released on **2024-04-27**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix the plugin string env and search error.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix the plugin string env and search error, closes [#2239](https://github.com/lobehub/lobe-chat/issues/2239) ([74b1ae0](https://github.com/lobehub/lobe-chat/commit/74b1ae0))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.150.4](https://github.com/lobehub/lobe-chat/compare/v0.150.3...v0.150.4)

<sup>Released on **2024-04-27**</sup>

#### 💄 Styles

- **misc**: Hide default model tag and show ollama provider by default.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Hide default model tag and show ollama provider by default, closes [#2238](https://github.com/lobehub/lobe-chat/issues/2238) ([baa4780](https://github.com/lobehub/lobe-chat/commit/baa4780))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.150.3](https://github.com/lobehub/lobe-chat/compare/v0.150.2...v0.150.3)

<sup>Released on **2024-04-27**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix docker build.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix docker build, closes [#2236](https://github.com/lobehub/lobe-chat/issues/2236) ([749a843](https://github.com/lobehub/lobe-chat/commit/749a843))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.150.2](https://github.com/lobehub/lobe-chat/compare/v0.150.1...v0.150.2)

<sup>Released on **2024-04-27**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix display error when using `DEFAULT_AGENT_CONFIG` env.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix display error when using `DEFAULT_AGENT_CONFIG` env, closes [#2231](https://github.com/lobehub/lobe-chat/issues/2231) ([42bc734](https://github.com/lobehub/lobe-chat/commit/42bc734))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.150.1](https://github.com/lobehub/lobe-chat/compare/v0.150.0...v0.150.1)

<sup>Released on **2024-04-27**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix display error when using DEFAULT_AGENT_CONFIG env.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix display error when using DEFAULT_AGENT_CONFIG env ([e46215c](https://github.com/lobehub/lobe-chat/commit/e46215c))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.150.0](https://github.com/lobehub/lobe-chat/compare/v0.149.6...v0.150.0)

<sup>Released on **2024-04-26**</sup>

#### ✨ Features

- **misc**: Support feature flags.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support feature flags, closes [#2213](https://github.com/lobehub/lobe-chat/issues/2213) ([4532be5](https://github.com/lobehub/lobe-chat/commit/4532be5))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.149.6](https://github.com/lobehub/lobe-chat/compare/v0.149.5...v0.149.6)

<sup>Released on **2024-04-26**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix ollama host issue.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix ollama host issue, closes [#2210](https://github.com/lobehub/lobe-chat/issues/2210) ([c49d4c7](https://github.com/lobehub/lobe-chat/commit/c49d4c7))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.149.5](https://github.com/lobehub/lobe-chat/compare/v0.149.4...v0.149.5)

<sup>Released on **2024-04-25**</sup>

#### ♻ Code Refactoring

- **misc**: Sperate SessionStore to a new AgentStore.

#### 🐛 Bug Fixes

- **misc**: Fix not handle ollama error correctly.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Sperate SessionStore to a new AgentStore, closes [#2197](https://github.com/lobehub/lobe-chat/issues/2197) ([49c0ba6](https://github.com/lobehub/lobe-chat/commit/49c0ba6))

#### What's fixed

- **misc**: Fix not handle ollama error correctly ([efdf174](https://github.com/lobehub/lobe-chat/commit/efdf174))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.149.4](https://github.com/lobehub/lobe-chat/compare/v0.149.3...v0.149.4)

<sup>Released on **2024-04-25**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix chat client request not support abort.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix chat client request not support abort, closes [#2193](https://github.com/lobehub/lobe-chat/issues/2193) ([d22ef2c](https://github.com/lobehub/lobe-chat/commit/d22ef2c))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.149.3](https://github.com/lobehub/lobe-chat/compare/v0.149.2...v0.149.3)

<sup>Released on **2024-04-25**</sup>

#### 💄 Styles

- **misc**: Add displaying the message "Reset Successfully.".

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add displaying the message "Reset Successfully.", closes [#2158](https://github.com/lobehub/lobe-chat/issues/2158) ([27913ef](https://github.com/lobehub/lobe-chat/commit/27913ef))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.149.2](https://github.com/lobehub/lobe-chat/compare/v0.149.1...v0.149.2)

<sup>Released on **2024-04-24**</sup>

#### 💄 Styles

- **misc**: Support to create ai message.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Support to create ai message, closes [#2185](https://github.com/lobehub/lobe-chat/issues/2185) ([ba3ba6a](https://github.com/lobehub/lobe-chat/commit/ba3ba6a))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.149.1](https://github.com/lobehub/lobe-chat/compare/v0.149.0...v0.149.1)

<sup>Released on **2024-04-24**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.149.0](https://github.com/lobehub/lobe-chat/compare/v0.148.10...v0.149.0)

<sup>Released on **2024-04-24**</sup>

#### ✨ Features

- **misc**: Fully support ollama with browser request mode.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Fully support ollama with browser request mode, closes [#2168](https://github.com/lobehub/lobe-chat/issues/2168) ([562d189](https://github.com/lobehub/lobe-chat/commit/562d189))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.148.10](https://github.com/lobehub/lobe-chat/compare/v0.148.9...v0.148.10)

<sup>Released on **2024-04-24**</sup>

#### 💄 Styles

- **misc**: Update Ollama model 240421.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update Ollama model 240421, closes [#2130](https://github.com/lobehub/lobe-chat/issues/2130) ([e797af0](https://github.com/lobehub/lobe-chat/commit/e797af0))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.148.9](https://github.com/lobehub/lobe-chat/compare/v0.148.8...v0.148.9)

<sup>Released on **2024-04-23**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor for session server mode.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor for session server mode, closes [#2163](https://github.com/lobehub/lobe-chat/issues/2163) ([e012597](https://github.com/lobehub/lobe-chat/commit/e012597))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.148.8](https://github.com/lobehub/lobe-chat/compare/v0.148.7...v0.148.8)

<sup>Released on **2024-04-23**</sup>

#### 💄 Styles

- **misc**: update some `gemini` deployment restrictions.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: update some `gemini` deployment restrictions, closes [#2149](https://github.com/lobehub/lobe-chat/issues/2149) ([6d36863](https://github.com/lobehub/lobe-chat/commit/6d36863))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.148.7](https://github.com/lobehub/lobe-chat/compare/v0.148.6...v0.148.7)

<sup>Released on **2024-04-23**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix qwen-1.5-chat-72B context window in togetherai.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix qwen-1.5-chat-72B context window in togetherai, closes [#2142](https://github.com/lobehub/lobe-chat/issues/2142) ([d0753cf](https://github.com/lobehub/lobe-chat/commit/d0753cf))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.148.6](https://github.com/lobehub/lobe-chat/compare/v0.148.5...v0.148.6)

<sup>Released on **2024-04-22**</sup>

#### 🐛 Bug Fixes

- **misc**: Add Windows Phone, iPadOS, BlackBerry OS, Linux OS and Chrome OS sync icons.

#### 💄 Styles

- **misc**: Support more model Icons: dbrx, command-r, openchat, rwkv, Bert-vits2, Stable Diffusion, WizardLM, adobe firefly, skylark.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Add Windows Phone, iPadOS, BlackBerry OS, Linux OS and Chrome OS sync icons, closes [#2139](https://github.com/lobehub/lobe-chat/issues/2139) ([8ed1f07](https://github.com/lobehub/lobe-chat/commit/8ed1f07))

#### Styles

- **misc**: Support more model Icons: dbrx, command-r, openchat, rwkv, Bert-vits2, Stable Diffusion, WizardLM, adobe firefly, skylark, closes [#2107](https://github.com/lobehub/lobe-chat/issues/2107) ([4268d8b](https://github.com/lobehub/lobe-chat/commit/4268d8b))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.148.5](https://github.com/lobehub/lobe-chat/compare/v0.148.4...v0.148.5)

<sup>Released on **2024-04-22**</sup>

#### 💄 Styles

- **misc**: Support together ai to fetch model list.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Support together ai to fetch model list, closes [#2138](https://github.com/lobehub/lobe-chat/issues/2138) ([e6d3e4a](https://github.com/lobehub/lobe-chat/commit/e6d3e4a))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.148.4](https://github.com/lobehub/lobe-chat/compare/v0.148.3...v0.148.4)

<sup>Released on **2024-04-21**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix model list menu not display correctly.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix model list menu not display correctly, closes [#2133](https://github.com/lobehub/lobe-chat/issues/2133) ([98c844b](https://github.com/lobehub/lobe-chat/commit/98c844b))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.148.3](https://github.com/lobehub/lobe-chat/compare/v0.148.2...v0.148.3)

<sup>Released on **2024-04-21**</sup>

#### 💄 Styles

- **ollama**: Show size info while download, support cancel donwload, optimize calculation for speed.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **ollama**: Show size info while download, support cancel donwload, optimize calculation for speed, closes [#1664](https://github.com/lobehub/lobe-chat/issues/1664) ([9b18f47](https://github.com/lobehub/lobe-chat/commit/9b18f47))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.148.2](https://github.com/lobehub/lobe-chat/compare/v0.148.1...v0.148.2)

<sup>Released on **2024-04-21**</sup>

#### 💄 Styles

- **misc**: Add LLaMA 3 in groq + Mixtral 8x22B model.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add LLaMA 3 in groq + Mixtral 8x22B model, closes [#2128](https://github.com/lobehub/lobe-chat/issues/2128) ([6144448](https://github.com/lobehub/lobe-chat/commit/6144448))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.148.1](https://github.com/lobehub/lobe-chat/compare/v0.148.0...v0.148.1)

<sup>Released on **2024-04-20**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix topic title not auto generate.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix topic title not auto generate, closes [#2114](https://github.com/lobehub/lobe-chat/issues/2114) ([5979de3](https://github.com/lobehub/lobe-chat/commit/5979de3))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.148.0](https://github.com/lobehub/lobe-chat/compare/v0.147.22...v0.148.0)

<sup>Released on **2024-04-20**</sup>

#### ✨ Features

- **misc**: Support chat completion call at client side.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support chat completion call at client side, closes [#2041](https://github.com/lobehub/lobe-chat/issues/2041) ([9f5858f](https://github.com/lobehub/lobe-chat/commit/9f5858f))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.22](https://github.com/lobehub/lobe-chat/compare/v0.147.21...v0.147.22)

<sup>Released on **2024-04-19**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.21](https://github.com/lobehub/lobe-chat/compare/v0.147.20...v0.147.21)

<sup>Released on **2024-04-19**</sup>

#### 💄 Styles

- **misc**: Optimized file upload buttons and prompts.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Optimized file upload buttons and prompts, closes [#2050](https://github.com/lobehub/lobe-chat/issues/2050) ([c23087e](https://github.com/lobehub/lobe-chat/commit/c23087e))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.20](https://github.com/lobehub/lobe-chat/compare/v0.147.19...v0.147.20)

<sup>Released on **2024-04-18**</sup>

#### 💄 Styles

- **misc**: Improve aync session experience.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve aync session experience, closes [#2075](https://github.com/lobehub/lobe-chat/issues/2075) ([0f3b19b](https://github.com/lobehub/lobe-chat/commit/0f3b19b))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.19](https://github.com/lobehub/lobe-chat/compare/v0.147.18...v0.147.19)

<sup>Released on **2024-04-18**</sup>

#### 💄 Styles

- **misc**: Add M and B support max token in ModelInfoTags.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add M and B support max token in ModelInfoTags, closes [#2073](https://github.com/lobehub/lobe-chat/issues/2073) ([a985d8f](https://github.com/lobehub/lobe-chat/commit/a985d8f))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.18](https://github.com/lobehub/lobe-chat/compare/v0.147.17...v0.147.18)

<sup>Released on **2024-04-17**</sup>

#### 💄 Styles

- **misc**: Add claude 3 opus to AWS Bedrock, remove custom models from providers, and update Perplexity model names.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add claude 3 opus to AWS Bedrock, closes [#2072](https://github.com/lobehub/lobe-chat/issues/2072) ([479f562](https://github.com/lobehub/lobe-chat/commit/479f562))
- **misc**: Remove custom models from providers, and update Perplexity model names, closes [#2069](https://github.com/lobehub/lobe-chat/issues/2069) ([e04754d](https://github.com/lobehub/lobe-chat/commit/e04754d))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.17](https://github.com/lobehub/lobe-chat/compare/v0.147.16...v0.147.17)

<sup>Released on **2024-04-16**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor service to a uniform interface.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor service to a uniform interface, closes [#2062](https://github.com/lobehub/lobe-chat/issues/2062) ([86779e2](https://github.com/lobehub/lobe-chat/commit/86779e2))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.16](https://github.com/lobehub/lobe-chat/compare/v0.147.15...v0.147.16)

<sup>Released on **2024-04-14**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the auth.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the auth, closes [#2043](https://github.com/lobehub/lobe-chat/issues/2043) ([37ecb41](https://github.com/lobehub/lobe-chat/commit/37ecb41))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.15](https://github.com/lobehub/lobe-chat/compare/v0.147.14...v0.147.15)

<sup>Released on **2024-04-14**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix tool call error with gpt-4-turbo.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix tool call error with gpt-4-turbo, closes [#2042](https://github.com/lobehub/lobe-chat/issues/2042) ([63d91b8](https://github.com/lobehub/lobe-chat/commit/63d91b8))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.14](https://github.com/lobehub/lobe-chat/compare/v0.147.13...v0.147.14)

<sup>Released on **2024-04-14**</sup>

#### 💄 Styles

- **misc**: Enable `gemini-1.5-pro-latest` model by default.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Enable `gemini-1.5-pro-latest` model by default, closes [#2034](https://github.com/lobehub/lobe-chat/issues/2034) ([e8c65a9](https://github.com/lobehub/lobe-chat/commit/e8c65a9))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.13](https://github.com/lobehub/lobe-chat/compare/v0.147.12...v0.147.13)

<sup>Released on **2024-04-14**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the service with browser db invoke.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the service with browser db invoke, closes [#2038](https://github.com/lobehub/lobe-chat/issues/2038) ([43a2791](https://github.com/lobehub/lobe-chat/commit/43a2791))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.12](https://github.com/lobehub/lobe-chat/compare/v0.147.11...v0.147.12)

<sup>Released on **2024-04-14**</sup>

#### ♻ Code Refactoring

- **misc**: Move client db to a new folder.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Move client db to a new folder, closes [#2037](https://github.com/lobehub/lobe-chat/issues/2037) ([ebe65bb](https://github.com/lobehub/lobe-chat/commit/ebe65bb))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.11](https://github.com/lobehub/lobe-chat/compare/v0.147.10...v0.147.11)

<sup>Released on **2024-04-14**</sup>

#### 🐛 Bug Fixes

- **misc**: Support drag or copy to upload file by model ability.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Support drag or copy to upload file by model ability, closes [#2016](https://github.com/lobehub/lobe-chat/issues/2016) ([2abe37e](https://github.com/lobehub/lobe-chat/commit/2abe37e))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.10](https://github.com/lobehub/lobe-chat/compare/v0.147.9...v0.147.10)

<sup>Released on **2024-04-13**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.9](https://github.com/lobehub/lobe-chat/compare/v0.147.8...v0.147.9)

<sup>Released on **2024-04-12**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix custom model list not display correctly.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix custom model list not display correctly, closes [#2009](https://github.com/lobehub/lobe-chat/issues/2009) ([7d0e220](https://github.com/lobehub/lobe-chat/commit/7d0e220))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.8](https://github.com/lobehub/lobe-chat/compare/v0.147.7...v0.147.8)

<sup>Released on **2024-04-12**</sup>

#### ♻ Code Refactoring

- **misc**: Update README.md.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Update README.md ([44b5a23](https://github.com/lobehub/lobe-chat/commit/44b5a23))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.7](https://github.com/lobehub/lobe-chat/compare/v0.147.6...v0.147.7)

<sup>Released on **2024-04-12**</sup>

#### 🐛 Bug Fixes

- **misc**: Pin next to `14.1.4` to fix deployment.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Pin next to `14.1.4` to fix deployment, closes [#1998](https://github.com/lobehub/lobe-chat/issues/1998) ([dfa1872](https://github.com/lobehub/lobe-chat/commit/dfa1872))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.6](https://github.com/lobehub/lobe-chat/compare/v0.147.5...v0.147.6)

<sup>Released on **2024-04-11**</sup>

#### 💄 Styles

- **misc**: Add GPT-4-turbo and 2024-04-09 Turbo Vision model and mistral new model name.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add GPT-4-turbo and 2024-04-09 Turbo Vision model and mistral new model name, closes [#1984](https://github.com/lobehub/lobe-chat/issues/1984) ([f1795b1](https://github.com/lobehub/lobe-chat/commit/f1795b1))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.5](https://github.com/lobehub/lobe-chat/compare/v0.147.4...v0.147.5)

<sup>Released on **2024-04-11**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix only search topics in current session.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix only search topics in current session, closes [#1834](https://github.com/lobehub/lobe-chat/issues/1834) ([9fdcfa4](https://github.com/lobehub/lobe-chat/commit/9fdcfa4))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.4](https://github.com/lobehub/lobe-chat/compare/v0.147.3...v0.147.4)

<sup>Released on **2024-04-11**</sup>

#### 🐛 Bug Fixes

- **misc**: Add more builtin OpenRouter models.

#### 💄 Styles

- **misc**: Adjust minimum width value for DraggablePanel component.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Add more builtin OpenRouter models, closes [#1973](https://github.com/lobehub/lobe-chat/issues/1973) ([0000b1a](https://github.com/lobehub/lobe-chat/commit/0000b1a))

#### Styles

- **misc**: Adjust minimum width value for DraggablePanel component, closes [#1901](https://github.com/lobehub/lobe-chat/issues/1901) ([a696d37](https://github.com/lobehub/lobe-chat/commit/a696d37))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.3](https://github.com/lobehub/lobe-chat/compare/v0.147.2...v0.147.3)

<sup>Released on **2024-04-11**</sup>

#### 💄 Styles

- **misc**: Support Google Proxy URL.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Support Google Proxy URL, closes [#1979](https://github.com/lobehub/lobe-chat/issues/1979) ([fbf2c24](https://github.com/lobehub/lobe-chat/commit/fbf2c24))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.2](https://github.com/lobehub/lobe-chat/compare/v0.147.1...v0.147.2)

<sup>Released on **2024-04-11**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix custom model not display correctly.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix custom model not display correctly, closes [#1972](https://github.com/lobehub/lobe-chat/issues/1972) ([5d7cae9](https://github.com/lobehub/lobe-chat/commit/5d7cae9))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.147.1](https://github.com/lobehub/lobe-chat/compare/v0.147.0...v0.147.1)

<sup>Released on **2024-04-11**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix normalizeLocale with first matching locale.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix normalizeLocale with first matching locale, closes [#1767](https://github.com/lobehub/lobe-chat/issues/1767) ([182ff23](https://github.com/lobehub/lobe-chat/commit/182ff23))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.147.0](https://github.com/lobehub/lobe-chat/compare/v0.146.2...v0.147.0)

<sup>Released on **2024-04-10**</sup>

#### ♻ Code Refactoring

- **misc**: Add db migration, add migrations from v3 to v4, clean openai azure code, refactor agent runtime with openai compatible factory, refactor api key form locale, refactor openAI to openai and azure, refactor the hidden to enabled, refactor the key, refactor the model config selector, refactor the route auth as a middleware, refactor the server config to migrate model provider env, refactor the server config to migrate model provider env, rename the key to enabledModels.

#### ✨ Features

- **misc**: Refactor to support azure openai provider, support close openai, support display model list, support model config modal, support model list with model providers, support open router auto model list, support openai model fetcher, support update model config, support user config model.

#### 🐛 Bug Fixes

- **misc**: Fix db migration, fix db migration.

#### 💄 Styles

- **misc**: Fix i18n of model list fetcher, improve detail design, improve logo style, update locale.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Add db migration ([6ceb818](https://github.com/lobehub/lobe-chat/commit/6ceb818))
- **misc**: Add migrations from v3 to v4 ([199ded2](https://github.com/lobehub/lobe-chat/commit/199ded2))
- **misc**: Clean openai azure code ([be4bcca](https://github.com/lobehub/lobe-chat/commit/be4bcca))
- **misc**: Refactor agent runtime with openai compatible factory ([89adf9d](https://github.com/lobehub/lobe-chat/commit/89adf9d))
- **misc**: Refactor api key form locale ([a069169](https://github.com/lobehub/lobe-chat/commit/a069169))
- **misc**: Refactor openAI to openai and azure ([2190a95](https://github.com/lobehub/lobe-chat/commit/2190a95))
- **misc**: Refactor the hidden to enabled ([78a1aac](https://github.com/lobehub/lobe-chat/commit/78a1aac))
- **misc**: Refactor the key ([d5c82f6](https://github.com/lobehub/lobe-chat/commit/d5c82f6))
- **misc**: Refactor the model config selector ([d865ca1](https://github.com/lobehub/lobe-chat/commit/d865ca1))
- **misc**: Refactor the route auth as a middleware ([ef5ee2a](https://github.com/lobehub/lobe-chat/commit/ef5ee2a))
- **misc**: Refactor the server config to migrate model provider env ([e4f110e](https://github.com/lobehub/lobe-chat/commit/e4f110e))
- **misc**: Refactor the server config to migrate model provider env ([c398063](https://github.com/lobehub/lobe-chat/commit/c398063))
- **misc**: Rename the key to enabledModels ([ebfa0aa](https://github.com/lobehub/lobe-chat/commit/ebfa0aa))

#### What's improved

- **misc**: Refactor to support azure openai provider ([d737afe](https://github.com/lobehub/lobe-chat/commit/d737afe))
- **misc**: Support close openai ([1ff1aef](https://github.com/lobehub/lobe-chat/commit/1ff1aef))
- **misc**: Support display model list ([e59635f](https://github.com/lobehub/lobe-chat/commit/e59635f))
- **misc**: Support model config modal ([62d6bb7](https://github.com/lobehub/lobe-chat/commit/62d6bb7))
- **misc**: Support model list with model providers, closes [#1916](https://github.com/lobehub/lobe-chat/issues/1916) ([0895dd2](https://github.com/lobehub/lobe-chat/commit/0895dd2))
- **misc**: Support open router auto model list ([1ba90d3](https://github.com/lobehub/lobe-chat/commit/1ba90d3))
- **misc**: Support openai model fetcher ([56032e6](https://github.com/lobehub/lobe-chat/commit/56032e6))
- **misc**: Support update model config ([e8ed847](https://github.com/lobehub/lobe-chat/commit/e8ed847))
- **misc**: Support user config model ([72fd873](https://github.com/lobehub/lobe-chat/commit/72fd873))

#### What's fixed

- **misc**: Fix db migration ([4e75074](https://github.com/lobehub/lobe-chat/commit/4e75074))
- **misc**: Fix db migration ([571b6dd](https://github.com/lobehub/lobe-chat/commit/571b6dd))

#### Styles

- **misc**: Fix i18n of model list fetcher ([67ed8c2](https://github.com/lobehub/lobe-chat/commit/67ed8c2))
- **misc**: Improve detail design ([adcce07](https://github.com/lobehub/lobe-chat/commit/adcce07))
- **misc**: Improve logo style ([c5826ce](https://github.com/lobehub/lobe-chat/commit/c5826ce))
- **misc**: Update locale ([021bf91](https://github.com/lobehub/lobe-chat/commit/021bf91))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.146.2](https://github.com/lobehub/lobe-chat/compare/v0.146.1...v0.146.2)

<sup>Released on **2024-04-10**</sup>

#### 🐛 Bug Fixes

- **misc**: Pin `ai@3.0.19` to fix error with chat stream output.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Pin `ai@3.0.19` to fix error with chat stream output, closes [#1946](https://github.com/lobehub/lobe-chat/issues/1946) ([07d4419](https://github.com/lobehub/lobe-chat/commit/07d4419))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.146.1](https://github.com/lobehub/lobe-chat/compare/v0.146.0...v0.146.1)

<sup>Released on **2024-04-10**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.146.0](https://github.com/lobehub/lobe-chat/compare/v0.145.13...v0.146.0)

<sup>Released on **2024-04-08**</sup>

#### ✨ Features

- **misc**: Add support for ZITADEL SSO provider.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add support for ZITADEL SSO provider, closes [#1904](https://github.com/lobehub/lobe-chat/issues/1904) ([44152f7](https://github.com/lobehub/lobe-chat/commit/44152f7))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.145.13](https://github.com/lobehub/lobe-chat/compare/v0.145.12...v0.145.13)

<sup>Released on **2024-04-07**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the model settings for more clean code.

#### 🐛 Bug Fixes

- **misc**: Fix normalize russian locale.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the model settings for more clean code, closes [#1906](https://github.com/lobehub/lobe-chat/issues/1906) ([db5d3ac](https://github.com/lobehub/lobe-chat/commit/db5d3ac))

#### What's fixed

- **misc**: Fix normalize russian locale, closes [#1903](https://github.com/lobehub/lobe-chat/issues/1903) ([e86b596](https://github.com/lobehub/lobe-chat/commit/e86b596))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.145.12](https://github.com/lobehub/lobe-chat/compare/v0.145.11...v0.145.12)

<sup>Released on **2024-04-04**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix typo of azure-id sso provider.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix typo of azure-id sso provider, closes [#1898](https://github.com/lobehub/lobe-chat/issues/1898) ([6925b25](https://github.com/lobehub/lobe-chat/commit/6925b25))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.145.11](https://github.com/lobehub/lobe-chat/compare/v0.145.10...v0.145.11)

<sup>Released on **2024-04-03**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix page crash when using browser as the stt engine.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix page crash when using browser as the stt engine, closes [#1884](https://github.com/lobehub/lobe-chat/issues/1884) ([278820a](https://github.com/lobehub/lobe-chat/commit/278820a))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.145.10](https://github.com/lobehub/lobe-chat/compare/v0.145.9...v0.145.10)

<sup>Released on **2024-04-02**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.145.9](https://github.com/lobehub/lobe-chat/compare/v0.145.8...v0.145.9)

<sup>Released on **2024-04-02**</sup>

#### 💄 Styles

- **misc**: Improve scrollbar style.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve scrollbar style, closes [#1869](https://github.com/lobehub/lobe-chat/issues/1869) ([33d857f](https://github.com/lobehub/lobe-chat/commit/33d857f))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.145.8](https://github.com/lobehub/lobe-chat/compare/v0.145.7...v0.145.8)

<sup>Released on **2024-04-02**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor SSO providers.

#### 🐛 Bug Fixes

- **misc**: Fix plugins dropdown menu overflow.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor SSO providers, closes [#1865](https://github.com/lobehub/lobe-chat/issues/1865) ([290d33b](https://github.com/lobehub/lobe-chat/commit/290d33b))

#### What's fixed

- **misc**: Fix plugins dropdown menu overflow, closes [#1855](https://github.com/lobehub/lobe-chat/issues/1855) ([00e9068](https://github.com/lobehub/lobe-chat/commit/00e9068))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.145.7](https://github.com/lobehub/lobe-chat/compare/v0.145.6...v0.145.7)

<sup>Released on **2024-04-02**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix DraggablePanel bar interfere with the operation of the scrollbar.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix DraggablePanel bar interfere with the operation of the scrollbar, closes [#1775](https://github.com/lobehub/lobe-chat/issues/1775) ([4b7b243](https://github.com/lobehub/lobe-chat/commit/4b7b243))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.145.6](https://github.com/lobehub/lobe-chat/compare/v0.145.5...v0.145.6)

<sup>Released on **2024-04-02**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.145.5](https://github.com/lobehub/lobe-chat/compare/v0.145.4...v0.145.5)

<sup>Released on **2024-03-30**</sup>

#### 🐛 Bug Fixes

- **misc**: Add qwen api models patch in ollama.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Add qwen api models patch in ollama, closes [#1630](https://github.com/lobehub/lobe-chat/issues/1630) ([a1e754c](https://github.com/lobehub/lobe-chat/commit/a1e754c))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.145.4](https://github.com/lobehub/lobe-chat/compare/v0.145.3...v0.145.4)

<sup>Released on **2024-03-29**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix plugin install loading state error.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix plugin install loading state error, closes [#1815](https://github.com/lobehub/lobe-chat/issues/1815) ([2412a73](https://github.com/lobehub/lobe-chat/commit/2412a73))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.145.3](https://github.com/lobehub/lobe-chat/compare/v0.145.2...v0.145.3)

<sup>Released on **2024-03-29**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix antd locale.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix antd locale, closes [#1814](https://github.com/lobehub/lobe-chat/issues/1814) ([e7fc148](https://github.com/lobehub/lobe-chat/commit/e7fc148))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.145.2](https://github.com/lobehub/lobe-chat/compare/v0.145.1...v0.145.2)

<sup>Released on **2024-03-29**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix google ultra model id.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix google ultra model id, closes [#1813](https://github.com/lobehub/lobe-chat/issues/1813) ([c96ba12](https://github.com/lobehub/lobe-chat/commit/c96ba12))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.145.1](https://github.com/lobehub/lobe-chat/compare/v0.145.0...v0.145.1)

<sup>Released on **2024-03-29**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix Google Gemini pro 1.5 and system role not take effect.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix Google Gemini pro 1.5 and system role not take effect, closes [#1801](https://github.com/lobehub/lobe-chat/issues/1801) ([0a3e3f7](https://github.com/lobehub/lobe-chat/commit/0a3e3f7))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.145.0](https://github.com/lobehub/lobe-chat/compare/v0.144.1...v0.145.0)

<sup>Released on **2024-03-29**</sup>

#### ✨ Features

- **misc**: Support TogetherAI as new model provider.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support TogetherAI as new model provider, closes [#1709](https://github.com/lobehub/lobe-chat/issues/1709) ([d6921ef](https://github.com/lobehub/lobe-chat/commit/d6921ef))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.144.1](https://github.com/lobehub/lobe-chat/compare/v0.144.0...v0.144.1)

<sup>Released on **2024-03-29**</sup>

#### 🐛 Bug Fixes

- **ollama**: Suppport vision for LLaVA models.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **ollama**: Suppport vision for LLaVA models, closes [#1791](https://github.com/lobehub/lobe-chat/issues/1791) ([e2d3de6](https://github.com/lobehub/lobe-chat/commit/e2d3de6))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.144.0](https://github.com/lobehub/lobe-chat/compare/v0.143.0...v0.144.0)

<sup>Released on **2024-03-29**</sup>

#### ✨ Features

- **misc**: Support authentik as sso.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support authentik as sso, closes [#1650](https://github.com/lobehub/lobe-chat/issues/1650) ([181dfa5](https://github.com/lobehub/lobe-chat/commit/181dfa5))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.143.0](https://github.com/lobehub/lobe-chat/compare/v0.142.9...v0.143.0)

<sup>Released on **2024-03-28**</sup>

#### ✨ Features

- **misc**: Add Bulgarian translation.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add Bulgarian translation, closes [#1732](https://github.com/lobehub/lobe-chat/issues/1732) ([e181dd1](https://github.com/lobehub/lobe-chat/commit/e181dd1))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.142.9](https://github.com/lobehub/lobe-chat/compare/v0.142.8...v0.142.9)

<sup>Released on **2024-03-28**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix Add agent and Converse button not jump.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix Add agent and Converse button not jump, closes [#1785](https://github.com/lobehub/lobe-chat/issues/1785) ([a52799c](https://github.com/lobehub/lobe-chat/commit/a52799c))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.142.8](https://github.com/lobehub/lobe-chat/compare/v0.142.7...v0.142.8)

<sup>Released on **2024-03-28**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix gemini 1.5 pro model id to support gemini new models.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix gemini 1.5 pro model id to support gemini new models, closes [#1776](https://github.com/lobehub/lobe-chat/issues/1776) ([591dcb3](https://github.com/lobehub/lobe-chat/commit/591dcb3))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.142.7](https://github.com/lobehub/lobe-chat/compare/v0.142.6...v0.142.7)

<sup>Released on **2024-03-27**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix the missing German locale.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix the missing German locale, closes [#1753](https://github.com/lobehub/lobe-chat/issues/1753) ([a452612](https://github.com/lobehub/lobe-chat/commit/a452612))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.142.6](https://github.com/lobehub/lobe-chat/compare/v0.142.5...v0.142.6)

<sup>Released on **2024-03-26**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix normalize german locale.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix normalize german locale, closes [#1750](https://github.com/lobehub/lobe-chat/issues/1750) ([69fcc78](https://github.com/lobehub/lobe-chat/commit/69fcc78))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.142.5](https://github.com/lobehub/lobe-chat/compare/v0.142.4...v0.142.5)

<sup>Released on **2024-03-26**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix mobile click, fix mobile click issue.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix mobile click ([3775b28](https://github.com/lobehub/lobe-chat/commit/3775b28))
- **misc**: Fix mobile click issue, closes [#1744](https://github.com/lobehub/lobe-chat/issues/1744) ([a6b1234](https://github.com/lobehub/lobe-chat/commit/a6b1234))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.142.4](https://github.com/lobehub/lobe-chat/compare/v0.142.3...v0.142.4)

<sup>Released on **2024-03-26**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.142.3](https://github.com/lobehub/lobe-chat/compare/v0.142.2...v0.142.3)

<sup>Released on **2024-03-26**</sup>

#### 🐛 Bug Fixes

- **misc**: Pin `next-auth` temporary to fix build error.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Pin `next-auth` temporary to fix build error, closes [#1739](https://github.com/lobehub/lobe-chat/issues/1739) ([e9ece9f](https://github.com/lobehub/lobe-chat/commit/e9ece9f))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.142.2](https://github.com/lobehub/lobe-chat/compare/v0.142.1...v0.142.2)

<sup>Released on **2024-03-25**</sup>

#### 🐛 Bug Fixes

- **misc**: Support openrouter custom models env.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Support openrouter custom models env, closes [#1647](https://github.com/lobehub/lobe-chat/issues/1647) ([78baa16](https://github.com/lobehub/lobe-chat/commit/78baa16))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.142.1](https://github.com/lobehub/lobe-chat/compare/v0.142.0...v0.142.1)

<sup>Released on **2024-03-25**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.142.0](https://github.com/lobehub/lobe-chat/compare/v0.141.2...v0.142.0)

<sup>Released on **2024-03-25**</sup>

#### ✨ Features

- **misc**: Support 01.AI as a new provider.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support 01.AI as a new provider, closes [#1627](https://github.com/lobehub/lobe-chat/issues/1627) ([08342fd](https://github.com/lobehub/lobe-chat/commit/08342fd))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.141.2](https://github.com/lobehub/lobe-chat/compare/v0.141.1...v0.141.2)

<sup>Released on **2024-03-22**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix window icon and scrollbar style.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix window icon and scrollbar style, closes [#1691](https://github.com/lobehub/lobe-chat/issues/1691) ([4f46845](https://github.com/lobehub/lobe-chat/commit/4f46845))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.141.1](https://github.com/lobehub/lobe-chat/compare/v0.141.0...v0.141.1)

<sup>Released on **2024-03-22**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the Vercel Aanlytics and support Google Aanlytics.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the Vercel Aanlytics and support Google Aanlytics, closes [#1688](https://github.com/lobehub/lobe-chat/issues/1688) ([e07e9cf](https://github.com/lobehub/lobe-chat/commit/e07e9cf))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.141.0](https://github.com/lobehub/lobe-chat/compare/v0.140.1...v0.141.0)

<sup>Released on **2024-03-22**</sup>

#### ✨ Features

- **misc**: Using YJS and WebRTC to support sync data between different devices.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Using YJS and WebRTC to support sync data between different devices, closes [#1525](https://github.com/lobehub/lobe-chat/issues/1525) ([60d9186](https://github.com/lobehub/lobe-chat/commit/60d9186))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.140.1](https://github.com/lobehub/lobe-chat/compare/v0.140.0...v0.140.1)

<sup>Released on **2024-03-22**</sup>

#### 💄 Styles

- **misc**: add Moonshot Kimi Reverse model to Moonshot model provider..

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: add Moonshot Kimi Reverse model to Moonshot model provider., closes [#1659](https://github.com/lobehub/lobe-chat/issues/1659) ([5bae263](https://github.com/lobehub/lobe-chat/commit/5bae263))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.140.0](https://github.com/lobehub/lobe-chat/compare/v0.139.2...v0.140.0)

<sup>Released on **2024-03-22**</sup>

#### ✨ Features

- **misc**: Add gemini 1.5 pro support.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add gemini 1.5 pro support, closes [#1669](https://github.com/lobehub/lobe-chat/issues/1669) ([2b280af](https://github.com/lobehub/lobe-chat/commit/2b280af))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.139.2](https://github.com/lobehub/lobe-chat/compare/v0.139.1...v0.139.2)

<sup>Released on **2024-03-22**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix code block display issue.

#### 💄 Styles

- **misc**: The bottom safe area height of iOS.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix code block display issue, closes [#1675](https://github.com/lobehub/lobe-chat/issues/1675) ([7707dda](https://github.com/lobehub/lobe-chat/commit/7707dda))

#### Styles

- **misc**: The bottom safe area height of iOS, closes [#1637](https://github.com/lobehub/lobe-chat/issues/1637) [#1181](https://github.com/lobehub/lobe-chat/issues/1181) ([44fae5e](https://github.com/lobehub/lobe-chat/commit/44fae5e))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.139.1](https://github.com/lobehub/lobe-chat/compare/v0.139.0...v0.139.1)

<sup>Released on **2024-03-17**</sup>

#### 💄 Styles

- **misc**: Improve model tags.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve model tags ([11cd5f4](https://github.com/lobehub/lobe-chat/commit/11cd5f4))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.139.0](https://github.com/lobehub/lobe-chat/compare/v0.138.2...v0.139.0)

<sup>Released on **2024-03-16**</sup>

#### ✨ Features

- **misc**: Support openrouter as a new model provider.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support openrouter as a new model provider, closes [#1572](https://github.com/lobehub/lobe-chat/issues/1572) ([780b1a2](https://github.com/lobehub/lobe-chat/commit/780b1a2))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.138.2](https://github.com/lobehub/lobe-chat/compare/v0.138.1...v0.138.2)

<sup>Released on **2024-03-15**</sup>

#### 💄 Styles

- **misc**: Update Markdown in ChatItem.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update Markdown in ChatItem ([be75549](https://github.com/lobehub/lobe-chat/commit/be75549))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.138.1](https://github.com/lobehub/lobe-chat/compare/v0.138.0...v0.138.1)

<sup>Released on **2024-03-15**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix URL typo.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix URL typo, closes [#1590](https://github.com/lobehub/lobe-chat/issues/1590) ([97137a9](https://github.com/lobehub/lobe-chat/commit/97137a9))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.138.0](https://github.com/lobehub/lobe-chat/compare/v0.137.0...v0.138.0)

<sup>Released on **2024-03-15**</sup>

#### ✨ Features

- **misc**: Support groq as a model provider.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support groq as a model provider, closes [#1569](https://github.com/lobehub/lobe-chat/issues/1569) [#1562](https://github.com/lobehub/lobe-chat/issues/1562) [#1570](https://github.com/lobehub/lobe-chat/issues/1570) ([a04c364](https://github.com/lobehub/lobe-chat/commit/a04c364))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.137.0](https://github.com/lobehub/lobe-chat/compare/v0.136.0...v0.137.0)

<sup>Released on **2024-03-15**</sup>

#### ✨ Features

- **ollama**: Improve connection check method and provide selector for user to control model options.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **ollama**: Improve connection check method and provide selector for user to control model options, closes [#1397](https://github.com/lobehub/lobe-chat/issues/1397) ([675902f](https://github.com/lobehub/lobe-chat/commit/675902f))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.136.0](https://github.com/lobehub/lobe-chat/compare/v0.135.4...v0.136.0)

<sup>Released on **2024-03-15**</sup>

#### ✨ Features

- **misc**: Support azure-ad as a new sso provider.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support azure-ad as a new sso provider, closes [#1456](https://github.com/lobehub/lobe-chat/issues/1456) ([6649cd1](https://github.com/lobehub/lobe-chat/commit/6649cd1))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.135.4](https://github.com/lobehub/lobe-chat/compare/v0.135.3...v0.135.4)

<sup>Released on **2024-03-15**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.135.3](https://github.com/lobehub/lobe-chat/compare/v0.135.2...v0.135.3)

<sup>Released on **2024-03-15**</sup>

#### 🐛 Bug Fixes

- **ollama**: Fix duplicate llama model and add llama2-chinese models.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **ollama**: Fix duplicate llama model and add llama2-chinese models, closes [#1579](https://github.com/lobehub/lobe-chat/issues/1579) ([6b9b5c8](https://github.com/lobehub/lobe-chat/commit/6b9b5c8))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.135.2](https://github.com/lobehub/lobe-chat/compare/v0.135.1...v0.135.2)

<sup>Released on **2024-03-14**</sup>

#### ♻ Code Refactoring

- **misc**: Upgrade plugin db schema.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Upgrade plugin db schema, closes [#1571](https://github.com/lobehub/lobe-chat/issues/1571) ([757574a](https://github.com/lobehub/lobe-chat/commit/757574a))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.135.1](https://github.com/lobehub/lobe-chat/compare/v0.135.0...v0.135.1)

<sup>Released on **2024-03-14**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the db model.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the db model, closes [#1567](https://github.com/lobehub/lobe-chat/issues/1567) ([3d56dd6](https://github.com/lobehub/lobe-chat/commit/3d56dd6))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.135.0](https://github.com/lobehub/lobe-chat/compare/v0.134.1...v0.135.0)

<sup>Released on **2024-03-14**</sup>

#### ✨ Features

- **misc**: Add claude 3 to bedrock provider.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add claude 3 to bedrock provider, closes [#1551](https://github.com/lobehub/lobe-chat/issues/1551) ([6e1fe33](https://github.com/lobehub/lobe-chat/commit/6e1fe33))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.134.1](https://github.com/lobehub/lobe-chat/compare/v0.134.0...v0.134.1)

<sup>Released on **2024-03-13**</sup>

#### 💄 Styles

- **misc**: Add more model display name.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add more model display name, closes [#1554](https://github.com/lobehub/lobe-chat/issues/1554) ([31c987a](https://github.com/lobehub/lobe-chat/commit/31c987a))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.134.0](https://github.com/lobehub/lobe-chat/compare/v0.133.5...v0.134.0)

<sup>Released on **2024-03-13**</sup>

#### ✨ Features

- **misc**: Support anthropic proxy url.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support anthropic proxy url, closes [#1529](https://github.com/lobehub/lobe-chat/issues/1529) ([a5a9257](https://github.com/lobehub/lobe-chat/commit/a5a9257))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.133.5](https://github.com/lobehub/lobe-chat/compare/v0.133.4...v0.133.5)

<sup>Released on **2024-03-12**</sup>

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.133.4](https://github.com/lobehub/lobe-chat/compare/v0.133.3...v0.133.4)

<sup>Released on **2024-03-11**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix sitemap missing in docker building.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix sitemap missing in docker building, closes [#1533](https://github.com/lobehub/lobe-chat/issues/1533) ([49752da](https://github.com/lobehub/lobe-chat/commit/49752da))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.133.3](https://github.com/lobehub/lobe-chat/compare/v0.133.2...v0.133.3)

<sup>Released on **2024-03-10**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix the max token of claude 3.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix the max token of claude 3, closes [#1526](https://github.com/lobehub/lobe-chat/issues/1526) ([222fae3](https://github.com/lobehub/lobe-chat/commit/222fae3))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.133.2](https://github.com/lobehub/lobe-chat/compare/v0.133.1...v0.133.2)

<sup>Released on **2024-03-10**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix qwen model id and improve anthropic logo text color.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix qwen model id and improve anthropic logo text color, closes [#1524](https://github.com/lobehub/lobe-chat/issues/1524) ([c68f5da](https://github.com/lobehub/lobe-chat/commit/c68f5da))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

<a name="readme-top" />

# Changelog

### [Version 0.133.1](https://github.com/lobehub/lobe-chat/compare/v0.133.0...v0.133.1)

<sup>Released on **2024-03-08**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix sitemap config.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix sitemap config ([a2542a7](https://github.com/lobehub/lobe-chat/commit/a2542a7))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.133.0](https://github.com/lobehub/lobe-chat/compare/v0.132.2...v0.133.0)

<sup>Released on **2024-03-07**</sup>

#### ✨ Features

- **misc**: Support Mistral model provider.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support Mistral model provider, closes [#1455](https://github.com/lobehub/lobe-chat/issues/1455) ([4f94bfe](https://github.com/lobehub/lobe-chat/commit/4f94bfe))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.132.2](https://github.com/lobehub/lobe-chat/compare/v0.132.1...v0.132.2)

<sup>Released on **2024-03-07**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix anthropic streaming on Vercel/Cloudflare.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix anthropic streaming on Vercel/Cloudflare, closes [#1480](https://github.com/lobehub/lobe-chat/issues/1480) ([227101a](https://github.com/lobehub/lobe-chat/commit/227101a))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 0.132.1](https://github.com/lobehub/lobe-chat/compare/v0.132.0...v0.132.1)

<sup>Released on **2024-03-06**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix hydration error while OAuth callback.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix hydration error while OAuth callback, closes [#1474](https://github.com/lobehub/lobe-chat/issues/1474) ([ff93825](https://github.com/lobehub/lobe-chat/commit/ff93825))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 0.132.0](https://github.com/lobehub/lobe-chat/compare/v0.131.0...v0.132.0)

<sup>Released on **2024-03-05**</sup>

#### ✨ Features

- **misc**: Support anthropic as model provider.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support anthropic as model provider, closes [#1409](https://github.com/lobehub/lobe-chat/issues/1409) ([a42cf8c](https://github.com/lobehub/lobe-chat/commit/a42cf8c))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.131.0](https://github.com/lobehub/lobe-chat/compare/v0.130.7...v0.131.0)

<sup>Released on **2024-03-05**</sup>

#### ✨ Features

- **misc**: Support langfuse integration.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support langfuse integration, closes [#1325](https://github.com/lobehub/lobe-chat/issues/1325) ([aaedfa7](https://github.com/lobehub/lobe-chat/commit/aaedfa7))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.130.7](https://github.com/lobehub/lobe-chat/compare/v0.130.6...v0.130.7)

<sup>Released on **2024-03-03**</sup>

#### ♻ Code Refactoring

- **misc**: Update gpt-3.5-turbo model card.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Update gpt-3.5-turbo model card, closes [#1449](https://github.com/lobehub/lobe-chat/issues/1449) ([d0be0c7](https://github.com/lobehub/lobe-chat/commit/d0be0c7))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.130.6](https://github.com/lobehub/lobe-chat/compare/v0.130.5...v0.130.6)

<sup>Released on **2024-03-01**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the plugin and tool slice.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the plugin and tool slice, closes [#1437](https://github.com/lobehub/lobe-chat/issues/1437) ([003e230](https://github.com/lobehub/lobe-chat/commit/003e230))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.130.5](https://github.com/lobehub/lobe-chat/compare/v0.130.4...v0.130.5)

<sup>Released on **2024-03-01**</sup>

#### 💄 Styles

- **misc**: Support switch model with tag.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Support switch model with tag, closes [#1435](https://github.com/lobehub/lobe-chat/issues/1435) ([233150e](https://github.com/lobehub/lobe-chat/commit/233150e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.130.4](https://github.com/lobehub/lobe-chat/compare/v0.130.3...v0.130.4)

<sup>Released on **2024-02-29**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the core chatStream and plugin gateway auth.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the core chatStream and plugin gateway auth, closes [#1426](https://github.com/lobehub/lobe-chat/issues/1426) ([7d3c1b6](https://github.com/lobehub/lobe-chat/commit/7d3c1b6))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.130.3](https://github.com/lobehub/lobe-chat/compare/v0.130.2...v0.130.3)

<sup>Released on **2024-02-29**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the google api route and add more tests for chat route.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the google api route and add more tests for chat route, closes [#1424](https://github.com/lobehub/lobe-chat/issues/1424) ([063a4d5](https://github.com/lobehub/lobe-chat/commit/063a4d5))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.130.2](https://github.com/lobehub/lobe-chat/compare/v0.130.1...v0.130.2)

<sup>Released on **2024-02-29**</sup>

#### 🐛 Bug Fixes

- **misc**: Update azure OpenAI api version options to latest.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Update azure OpenAI api version options to latest, closes [#1423](https://github.com/lobehub/lobe-chat/issues/1423) ([d992262](https://github.com/lobehub/lobe-chat/commit/d992262))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.130.1](https://github.com/lobehub/lobe-chat/compare/v0.130.0...v0.130.1)

<sup>Released on **2024-02-28**</sup>

#### 🐛 Bug Fixes

- **google**: Add safetySettings to avoid error response with google AI model.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **google**: Add safetySettings to avoid error response with google AI model, closes [#1418](https://github.com/lobehub/lobe-chat/issues/1418) ([87bf1fb](https://github.com/lobehub/lobe-chat/commit/87bf1fb))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.130.0](https://github.com/lobehub/lobe-chat/compare/v0.129.6...v0.130.0)

<sup>Released on **2024-02-27**</sup>

#### ✨ Features

- **misc**: Support multiple API Keys.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support multiple API Keys, closes [#1345](https://github.com/lobehub/lobe-chat/issues/1345) ([17c5da3](https://github.com/lobehub/lobe-chat/commit/17c5da3))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.129.6](https://github.com/lobehub/lobe-chat/compare/v0.129.5...v0.129.6)

<sup>Released on **2024-02-25**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix github url.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix github url ([42ea0f5](https://github.com/lobehub/lobe-chat/commit/42ea0f5))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.129.5](https://github.com/lobehub/lobe-chat/compare/v0.129.4...v0.129.5)

<sup>Released on **2024-02-25**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix eliminate UI jitter on navigation, improving experience for users sensitive to motion.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix eliminate UI jitter on navigation, improving experience for users sensitive to motion, closes [#1381](https://github.com/lobehub/lobe-chat/issues/1381) ([9231413](https://github.com/lobehub/lobe-chat/commit/9231413))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.129.4](https://github.com/lobehub/lobe-chat/compare/v0.129.3...v0.129.4)

<sup>Released on **2024-02-24**</sup>

#### 🐛 Bug Fixes

- **ollama**: Fix gemma model id.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **ollama**: Fix gemma model id, closes [#1377](https://github.com/lobehub/lobe-chat/issues/1377) ([3da50ff](https://github.com/lobehub/lobe-chat/commit/3da50ff))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.129.3](https://github.com/lobehub/lobe-chat/compare/v0.129.2...v0.129.3)

<sup>Released on **2024-02-23**</sup>

#### 💄 Styles

- **misc**: Add gemma model logo for ollama.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add gemma model logo for ollama, closes [#1369](https://github.com/lobehub/lobe-chat/issues/1369) ([e2fb3a3](https://github.com/lobehub/lobe-chat/commit/e2fb3a3))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.129.2](https://github.com/lobehub/lobe-chat/compare/v0.129.1...v0.129.2)

<sup>Released on **2024-02-23**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix OAuth don't get user id from session.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix OAuth don't get user id from session, closes [#1347](https://github.com/lobehub/lobe-chat/issues/1347) ([ce4d6ca](https://github.com/lobehub/lobe-chat/commit/ce4d6ca))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.129.1](https://github.com/lobehub/lobe-chat/compare/v0.129.0...v0.129.1)

<sup>Released on **2024-02-22**</sup>

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>
</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.129.0](https://github.com/lobehub/lobe-chat/compare/v0.128.10...v0.129.0)

<sup>Released on **2024-02-22**</sup>

#### ✨ Features

- **misc**: Support perplexity AI provider.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support perplexity AI provider, closes [#1339](https://github.com/lobehub/lobe-chat/issues/1339) ([61c88fb](https://github.com/lobehub/lobe-chat/commit/61c88fb))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.128.10](https://github.com/lobehub/lobe-chat/compare/v0.128.9...v0.128.10)

<sup>Released on **2024-02-21**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix the robots.txt config.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix the robots.txt config ([c4adfe4](https://github.com/lobehub/lobe-chat/commit/c4adfe4))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.128.9](https://github.com/lobehub/lobe-chat/compare/v0.128.8...v0.128.9)

<sup>Released on **2024-02-20**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix the robots.txt config.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix the robots.txt config ([34901b4](https://github.com/lobehub/lobe-chat/commit/34901b4))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.128.8](https://github.com/lobehub/lobe-chat/compare/v0.128.7...v0.128.8)

<sup>Released on **2024-02-20**</sup>

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>
</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.128.7](https://github.com/lobehub/lobe-chat/compare/v0.128.6...v0.128.7)

<sup>Released on **2024-02-20**</sup>

#### 💄 Styles

- **misc**: Improve docs url and add more docs.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve docs url and add more docs, closes [#1329](https://github.com/lobehub/lobe-chat/issues/1329) ([85b3136](https://github.com/lobehub/lobe-chat/commit/85b3136))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.128.6](https://github.com/lobehub/lobe-chat/compare/v0.128.5...v0.128.6)

<sup>Released on **2024-02-20**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix OAuth errors on Docker deployment.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix OAuth errors on Docker deployment, closes [#1323](https://github.com/lobehub/lobe-chat/issues/1323) ([922e843](https://github.com/lobehub/lobe-chat/commit/922e843))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.128.5](https://github.com/lobehub/lobe-chat/compare/v0.128.4...v0.128.5)

<sup>Released on **2024-02-18**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix the document url.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix the document url ([43b5677](https://github.com/lobehub/lobe-chat/commit/43b5677))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.128.4](https://github.com/lobehub/lobe-chat/compare/v0.128.3...v0.128.4)

<sup>Released on **2024-02-18**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix documents i18n.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix documents i18n, closes [#1319](https://github.com/lobehub/lobe-chat/issues/1319) ([a0600dc](https://github.com/lobehub/lobe-chat/commit/a0600dc))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.128.3](https://github.com/lobehub/lobe-chat/compare/v0.128.2...v0.128.3)

<sup>Released on **2024-02-18**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor with chat docs site.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor with chat docs site, closes [#1309](https://github.com/lobehub/lobe-chat/issues/1309) ([c131fa6](https://github.com/lobehub/lobe-chat/commit/c131fa6))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.128.2](https://github.com/lobehub/lobe-chat/compare/v0.128.1...v0.128.2)

<sup>Released on **2024-02-15**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix agent avatar click wrong navigation.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix agent avatar click wrong navigation, closes [#1308](https://github.com/lobehub/lobe-chat/issues/1308) ([adc7bc1](https://github.com/lobehub/lobe-chat/commit/adc7bc1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.128.1](https://github.com/lobehub/lobe-chat/compare/v0.128.0...v0.128.1)

<sup>Released on **2024-02-15**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix auto lang switch.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix auto lang switch, closes [#1305](https://github.com/lobehub/lobe-chat/issues/1305) ([7a51329](https://github.com/lobehub/lobe-chat/commit/7a51329))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.128.0](https://github.com/lobehub/lobe-chat/compare/v0.127.2...v0.128.0)

<sup>Released on **2024-02-14**</sup>

#### ✨ Features

- **misc**: Support define default agent config with `DEFAULT_AGENT_CONFIG` ENV.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support define default agent config with `DEFAULT_AGENT_CONFIG` ENV, closes [#1291](https://github.com/lobehub/lobe-chat/issues/1291) ([c7c096e](https://github.com/lobehub/lobe-chat/commit/c7c096e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.127.2](https://github.com/lobehub/lobe-chat/compare/v0.127.1...v0.127.2)

<sup>Released on **2024-02-14**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the sidebar to fix first render state.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the sidebar to fix first render state, closes [#1301](https://github.com/lobehub/lobe-chat/issues/1301) ([c477491](https://github.com/lobehub/lobe-chat/commit/c477491))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.127.1](https://github.com/lobehub/lobe-chat/compare/v0.127.0...v0.127.1)

<sup>Released on **2024-02-14**</sup>

#### 💄 Styles

- **misc**: Improve settings tabs style and refactor the LLM setting page.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve settings tabs style and refactor the LLM setting page, closes [#1299](https://github.com/lobehub/lobe-chat/issues/1299) ([31f6f15](https://github.com/lobehub/lobe-chat/commit/31f6f15))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.127.0](https://github.com/lobehub/lobe-chat/compare/v0.126.5...v0.127.0)

<sup>Released on **2024-02-13**</sup>

#### ✨ Features

- **llm**: Support Ollama AI Provider for local LLM.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **llm**: Support Ollama AI Provider for local LLM ([3b6f249](https://github.com/lobehub/lobe-chat/commit/3b6f249))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.126.5](https://github.com/lobehub/lobe-chat/compare/v0.126.4...v0.126.5)

<sup>Released on **2024-02-12**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor with the auth code.

#### 🐛 Bug Fixes

- **misc**: Fix middleware auth console error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor with the auth code ([8cee01f](https://github.com/lobehub/lobe-chat/commit/8cee01f))

#### What's fixed

- **misc**: Fix middleware auth console error ([ad3ade8](https://github.com/lobehub/lobe-chat/commit/ad3ade8))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.126.4](https://github.com/lobehub/lobe-chat/compare/v0.126.3...v0.126.4)

<sup>Released on **2024-02-11**</sup>

#### ♻ Code Refactoring

- **misc**: Update Model provider request url.

#### 🐛 Bug Fixes

- **misc**: Fix auth error in console, fix token tag usage display.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Update Model provider request url ([b64acc0](https://github.com/lobehub/lobe-chat/commit/b64acc0))

#### What's fixed

- **misc**: Fix auth error in console ([8e7ee82](https://github.com/lobehub/lobe-chat/commit/8e7ee82))
- **misc**: Fix token tag usage display ([6e7134b](https://github.com/lobehub/lobe-chat/commit/6e7134b))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.126.3](https://github.com/lobehub/lobe-chat/compare/v0.126.2...v0.126.3)

<sup>Released on **2024-02-09**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix auth layout error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix auth layout error ([efd7d14](https://github.com/lobehub/lobe-chat/commit/efd7d14))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.126.2](https://github.com/lobehub/lobe-chat/compare/v0.126.1...v0.126.2)

<sup>Released on **2024-02-09**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix OAuth throws an error on Vercel deploy.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix OAuth throws an error on Vercel deploy, closes [#1278](https://github.com/lobehub/lobe-chat/issues/1278) [#1277](https://github.com/lobehub/lobe-chat/issues/1277) [#1274](https://github.com/lobehub/lobe-chat/issues/1274) ([81d2d76](https://github.com/lobehub/lobe-chat/commit/81d2d76))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.126.1](https://github.com/lobehub/lobe-chat/compare/v0.126.0...v0.126.1)

<sup>Released on **2024-02-09**</sup>

#### 🐛 Bug Fixes

- **misc**: Add basePath to support subdirectory.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Add basePath to support subdirectory, closes [#1179](https://github.com/lobehub/lobe-chat/issues/1179) ([43e544a](https://github.com/lobehub/lobe-chat/commit/43e544a))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.126.0](https://github.com/lobehub/lobe-chat/compare/v0.125.0...v0.126.0)

<sup>Released on **2024-02-09**</sup>

#### ✨ Features

- **misc**: Support umami analytics.

#### 🐛 Bug Fixes

- **misc**: The back button on the chat setting page can correctly return to the configured Agent chat page.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support umami analytics, closes [#1267](https://github.com/lobehub/lobe-chat/issues/1267) ([da7beba](https://github.com/lobehub/lobe-chat/commit/da7beba))

#### What's fixed

- **misc**: The back button on the chat setting page can correctly return to the configured Agent chat page, closes [#1272](https://github.com/lobehub/lobe-chat/issues/1272) ([4cc1ad5](https://github.com/lobehub/lobe-chat/commit/4cc1ad5))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.125.0](https://github.com/lobehub/lobe-chat/compare/v0.124.3...v0.125.0)

<sup>Released on **2024-02-08**</sup>

#### ✨ Features

- **misc**: Support login & session authentication via OAuth 2.0 (Auth0).

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support login & session authentication via OAuth 2.0 (Auth0), closes [#1143](https://github.com/lobehub/lobe-chat/issues/1143) ([0609690](https://github.com/lobehub/lobe-chat/commit/0609690))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.124.3](https://github.com/lobehub/lobe-chat/compare/v0.124.2...v0.124.3)

<sup>Released on **2024-02-07**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix use azure api key error.

#### 💄 Styles

- **settings**: Improve LLM connection checker style.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix use azure api key error, closes [#1263](https://github.com/lobehub/lobe-chat/issues/1263) ([4e08f63](https://github.com/lobehub/lobe-chat/commit/4e08f63))

#### Styles

- **settings**: Improve LLM connection checker style, closes [#1252](https://github.com/lobehub/lobe-chat/issues/1252) ([4905d9e](https://github.com/lobehub/lobe-chat/commit/4905d9e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.124.2](https://github.com/lobehub/lobe-chat/compare/v0.124.1...v0.124.2)

<sup>Released on **2024-02-06**</sup>

#### 💄 Styles

- **misc**: Add moonshot i18n.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add moonshot i18n, closes [#1251](https://github.com/lobehub/lobe-chat/issues/1251) ([4b6663b](https://github.com/lobehub/lobe-chat/commit/4b6663b))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.124.1](https://github.com/lobehub/lobe-chat/compare/v0.124.0...v0.124.1)

<sup>Released on **2024-02-06**</sup>

#### 💄 Styles

- **misc**: Improve direction UX.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve direction UX, closes [#1169](https://github.com/lobehub/lobe-chat/issues/1169) ([e3929dc](https://github.com/lobehub/lobe-chat/commit/e3929dc))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.124.0](https://github.com/lobehub/lobe-chat/compare/v0.123.4...v0.124.0)

<sup>Released on **2024-02-06**</sup>

#### ✨ Features

- **misc**: Support Moonshot AI Provider.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support Moonshot AI Provider, closes [#1232](https://github.com/lobehub/lobe-chat/issues/1232) ([a6de202](https://github.com/lobehub/lobe-chat/commit/a6de202))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.123.4](https://github.com/lobehub/lobe-chat/compare/v0.123.3...v0.123.4)

<sup>Released on **2024-02-06**</sup>

#### 💄 Styles

- **misc**: Improve clear topic tips.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve clear topic tips, closes [#1247](https://github.com/lobehub/lobe-chat/issues/1247) ([2d133e9](https://github.com/lobehub/lobe-chat/commit/2d133e9))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.123.3](https://github.com/lobehub/lobe-chat/compare/v0.123.2...v0.123.3)

<sup>Released on **2024-02-06**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix non-https `crypto.subtile` missing error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix non-https `crypto.subtile` missing error, closes [#1238](https://github.com/lobehub/lobe-chat/issues/1238) ([1750d0b](https://github.com/lobehub/lobe-chat/commit/1750d0b))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.123.2](https://github.com/lobehub/lobe-chat/compare/v0.123.1...v0.123.2)

<sup>Released on **2024-02-06**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix docker build.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix docker build, closes [#1231](https://github.com/lobehub/lobe-chat/issues/1231) ([e180722](https://github.com/lobehub/lobe-chat/commit/e180722))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.123.1](https://github.com/lobehub/lobe-chat/compare/v0.123.0...v0.123.1)

<sup>Released on **2024-02-05**</sup>

#### 🐛 Bug Fixes

- **misc**: Improve auth control of plugin gateways, update dockerfile.

#### 💄 Styles

- **misc**: Add gpt-4-all feature flag.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Improve auth control of plugin gateways ([6354ad8](https://github.com/lobehub/lobe-chat/commit/6354ad8))
- **misc**: Update dockerfile ([e66aed3](https://github.com/lobehub/lobe-chat/commit/e66aed3))

#### Styles

- **misc**: Add gpt-4-all feature flag ([360b36c](https://github.com/lobehub/lobe-chat/commit/360b36c))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.123.0](https://github.com/lobehub/lobe-chat/compare/v0.122.9...v0.123.0)

<sup>Released on **2024-02-05**</sup>

#### ✨ Features

- **misc**: Support Google / Zhipu / AWS Bedrock model providers.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support Google / Zhipu / AWS Bedrock model providers, closes [#1173](https://github.com/lobehub/lobe-chat/issues/1173) ([d5929f6](https://github.com/lobehub/lobe-chat/commit/d5929f6))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.122.9](https://github.com/lobehub/lobe-chat/compare/v0.122.8...v0.122.9)

<sup>Released on **2024-02-05**</sup>

#### 💄 Styles

- **settings**: Improve LLM connection checker style.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **settings**: Improve LLM connection checker style, closes [#1222](https://github.com/lobehub/lobe-chat/issues/1222) ([8c349a1](https://github.com/lobehub/lobe-chat/commit/8c349a1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.122.8](https://github.com/lobehub/lobe-chat/compare/v0.122.7...v0.122.8)

<sup>Released on **2024-02-03**</sup>

#### 💄 Styles

- **misc**: Allow user to add agent without redirection.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Allow user to add agent without redirection, closes [#1199](https://github.com/lobehub/lobe-chat/issues/1199) ([6577ca1](https://github.com/lobehub/lobe-chat/commit/6577ca1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.122.7](https://github.com/lobehub/lobe-chat/compare/v0.122.6...v0.122.7)

<sup>Released on **2024-02-02**</sup>

#### 💄 Styles

- **misc**: Update the gpt-4-1106-preview model to gpt-4-0125-preview.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update the gpt-4-1106-preview model to gpt-4-0125-preview, closes [#1210](https://github.com/lobehub/lobe-chat/issues/1210) ([fe623e1](https://github.com/lobehub/lobe-chat/commit/fe623e1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.122.6](https://github.com/lobehub/lobe-chat/compare/v0.122.5...v0.122.6)

<sup>Released on **2024-01-31**</sup>

#### 🐛 Bug Fixes

- **check**: The state of connectivity can only be singular.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **check**: The state of connectivity can only be singular, closes [#1201](https://github.com/lobehub/lobe-chat/issues/1201) ([c412baf](https://github.com/lobehub/lobe-chat/commit/c412baf))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.122.5](https://github.com/lobehub/lobe-chat/compare/v0.122.4...v0.122.5)

<sup>Released on **2024-01-31**</sup>

#### 🐛 Bug Fixes

- **misc**: The plugin has a hallucination and gets stuck.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: The plugin has a hallucination and gets stuck, closes [#1191](https://github.com/lobehub/lobe-chat/issues/1191) ([0189759](https://github.com/lobehub/lobe-chat/commit/0189759))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.122.4](https://github.com/lobehub/lobe-chat/compare/v0.122.3...v0.122.4)

<sup>Released on **2024-01-30**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix plugin gateway auth.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix plugin gateway auth, closes [#1195](https://github.com/lobehub/lobe-chat/issues/1195) ([2184167](https://github.com/lobehub/lobe-chat/commit/2184167))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.122.3](https://github.com/lobehub/lobe-chat/compare/v0.122.2...v0.122.3)

<sup>Released on **2024-01-30**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the setting storage from localStorage to indexedDB.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the setting storage from localStorage to indexedDB, closes [#1180](https://github.com/lobehub/lobe-chat/issues/1180) ([615e796](https://github.com/lobehub/lobe-chat/commit/615e796))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.122.2](https://github.com/lobehub/lobe-chat/compare/v0.122.1...v0.122.2)

<sup>Released on **2024-01-30**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix unexpected topic switch when favoriting topic.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix unexpected topic switch when favoriting topic, closes [#1186](https://github.com/lobehub/lobe-chat/issues/1186) ([ab4de13](https://github.com/lobehub/lobe-chat/commit/ab4de13))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.122.1](https://github.com/lobehub/lobe-chat/compare/v0.122.0...v0.122.1)

<sup>Released on **2024-01-29**</sup>

#### 💄 Styles

- **misc**: Fix antd tab width flicker when show function debug.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix antd tab width flicker when show function debug, closes [#1171](https://github.com/lobehub/lobe-chat/issues/1171) ([14e99d2](https://github.com/lobehub/lobe-chat/commit/14e99d2))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.122.0](https://github.com/lobehub/lobe-chat/compare/v0.121.4...v0.122.0)

<sup>Released on **2024-01-29**</sup>

#### ✨ Features

- **misc**: Add create agent action in group menu.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add create agent action in group menu, closes [#1177](https://github.com/lobehub/lobe-chat/issues/1177) ([17ec1eb](https://github.com/lobehub/lobe-chat/commit/17ec1eb))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.121.4](https://github.com/lobehub/lobe-chat/compare/v0.121.3...v0.121.4)

<sup>Released on **2024-01-29**</sup>

#### 🐛 Bug Fixes

- **misc**: Pin ahooks to fix test ci and settings crash.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Pin ahooks to fix test ci and settings crash, closes [#1178](https://github.com/lobehub/lobe-chat/issues/1178) ([bc223a4](https://github.com/lobehub/lobe-chat/commit/bc223a4))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.121.3](https://github.com/lobehub/lobe-chat/compare/v0.121.2...v0.121.3)

<sup>Released on **2024-01-26**</sup>

#### 💄 Styles

- **misc**: Improve stop loading icon.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve stop loading icon, closes [#1154](https://github.com/lobehub/lobe-chat/issues/1154) ([6444fc2](https://github.com/lobehub/lobe-chat/commit/6444fc2))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.121.2](https://github.com/lobehub/lobe-chat/compare/v0.121.1...v0.121.2)

<sup>Released on **2024-01-25**</sup>

#### 💄 Styles

- **misc**: Remove centered prop from CreateGroupModal.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Remove centered prop from CreateGroupModal, closes [#1146](https://github.com/lobehub/lobe-chat/issues/1146) ([7b01676](https://github.com/lobehub/lobe-chat/commit/7b01676))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.121.1](https://github.com/lobehub/lobe-chat/compare/v0.121.0...v0.121.1)

<sup>Released on **2024-01-24**</sup>

#### 🐛 Bug Fixes

- **misc**: Automatically fill in the wrong password.

#### 💄 Styles

- **misc**: Fix default plugins height unstabled when scrolling.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Automatically fill in the wrong password, closes [#1144](https://github.com/lobehub/lobe-chat/issues/1144) ([0159a1a](https://github.com/lobehub/lobe-chat/commit/0159a1a))

#### Styles

- **misc**: Fix default plugins height unstabled when scrolling, closes [#1142](https://github.com/lobehub/lobe-chat/issues/1142) ([abed417](https://github.com/lobehub/lobe-chat/commit/abed417))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.121.0](https://github.com/lobehub/lobe-chat/compare/v0.120.6...v0.121.0)

<sup>Released on **2024-01-24**</sup>

#### ✨ Features

- **misc**: Add session group manager.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add session group manager, closes [#1055](https://github.com/lobehub/lobe-chat/issues/1055) [#1045](https://github.com/lobehub/lobe-chat/issues/1045) [#1126](https://github.com/lobehub/lobe-chat/issues/1126) [#1120](https://github.com/lobehub/lobe-chat/issues/1120) ([e3281fc](https://github.com/lobehub/lobe-chat/commit/e3281fc))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.120.6](https://github.com/lobehub/lobe-chat/compare/v0.120.5...v0.120.6)

<sup>Released on **2024-01-22**</sup>

#### 💄 Styles

- **misc**: Fix share image tags not align.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix share image tags not align, closes [#1047](https://github.com/lobehub/lobe-chat/issues/1047) ([28206b6](https://github.com/lobehub/lobe-chat/commit/28206b6))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.120.5](https://github.com/lobehub/lobe-chat/compare/v0.120.4...v0.120.5)

<sup>Released on **2024-01-21**</sup>

#### 💄 Styles

- **misc**: Update locale and add test for globalStore.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update locale and add test for globalStore, closes [#1119](https://github.com/lobehub/lobe-chat/issues/1119) ([4545cf0](https://github.com/lobehub/lobe-chat/commit/4545cf0))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.120.4](https://github.com/lobehub/lobe-chat/compare/v0.120.3...v0.120.4)

<sup>Released on **2024-01-21**</sup>

#### 🐛 Bug Fixes

- **misc**: Refactor url state management and fix some detail experience.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Refactor url state management and fix some detail experience, closes [#1117](https://github.com/lobehub/lobe-chat/issues/1117) ([a355d2c](https://github.com/lobehub/lobe-chat/commit/a355d2c))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.120.3](https://github.com/lobehub/lobe-chat/compare/v0.120.2...v0.120.3)

<sup>Released on **2024-01-19**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor antd i18n and improve locale order.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor antd i18n and improve locale order, closes [#1103](https://github.com/lobehub/lobe-chat/issues/1103) [#1083](https://github.com/lobehub/lobe-chat/issues/1083) ([c89f527](https://github.com/lobehub/lobe-chat/commit/c89f527))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.120.2](https://github.com/lobehub/lobe-chat/compare/v0.120.1...v0.120.2)

<sup>Released on **2024-01-17**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix setPluginMessage can not stop create ai message.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix setPluginMessage can not stop create ai message, closes [#1078](https://github.com/lobehub/lobe-chat/issues/1078) ([67de28d](https://github.com/lobehub/lobe-chat/commit/67de28d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.120.1](https://github.com/lobehub/lobe-chat/compare/v0.120.0...v0.120.1)

<sup>Released on **2024-01-16**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix list scrolling white screen on mobile.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix list scrolling white screen on mobile, closes [#1072](https://github.com/lobehub/lobe-chat/issues/1072) ([af10947](https://github.com/lobehub/lobe-chat/commit/af10947))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.120.0](https://github.com/lobehub/lobe-chat/compare/v0.119.13...v0.120.0)

<sup>Released on **2024-01-15**</sup>

#### ✨ Features

- **misc**: Adding Arabic Language Support.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Adding Arabic Language Support, closes [#1049](https://github.com/lobehub/lobe-chat/issues/1049) ([a325ef9](https://github.com/lobehub/lobe-chat/commit/a325ef9))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.119.13](https://github.com/lobehub/lobe-chat/compare/v0.119.12...v0.119.13)

<sup>Released on **2024-01-10**</sup>

#### 💄 Styles

- **misc**: Add delete and regenerate for function message.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add delete and regenerate for function message, closes [#992](https://github.com/lobehub/lobe-chat/issues/992) ([7f8c717](https://github.com/lobehub/lobe-chat/commit/7f8c717))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.119.12](https://github.com/lobehub/lobe-chat/compare/v0.119.11...v0.119.12)

<sup>Released on **2024-01-09**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix new line after sending messages with enter key.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix new line after sending messages with enter key, closes [#990](https://github.com/lobehub/lobe-chat/issues/990) ([e6ab019](https://github.com/lobehub/lobe-chat/commit/e6ab019))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.119.11](https://github.com/lobehub/lobe-chat/compare/v0.119.10...v0.119.11)

<sup>Released on **2024-01-09**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor ChatInput to support cmd+enter.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor ChatInput to support cmd+enter, closes [#983](https://github.com/lobehub/lobe-chat/issues/983) ([437a223](https://github.com/lobehub/lobe-chat/commit/437a223))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.119.10](https://github.com/lobehub/lobe-chat/compare/v0.119.9...v0.119.10)

<sup>Released on **2024-01-08**</sup>

#### 🐛 Bug Fixes

- **misc**: Debug information cannot be selected.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Debug information cannot be selected, closes [#980](https://github.com/lobehub/lobe-chat/issues/980) ([f02612d](https://github.com/lobehub/lobe-chat/commit/f02612d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.119.9](https://github.com/lobehub/lobe-chat/compare/v0.119.8...v0.119.9)

<sup>Released on **2024-01-08**</sup>

#### 💄 Styles

- **misc**: Fix ChatInput fullscreen display not correct.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix ChatInput fullscreen display not correct, closes [#982](https://github.com/lobehub/lobe-chat/issues/982) ([e4012c4](https://github.com/lobehub/lobe-chat/commit/e4012c4))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.119.8](https://github.com/lobehub/lobe-chat/compare/v0.119.7...v0.119.8)

<sup>Released on **2024-01-07**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix spotting tool call correctly.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix spotting tool call correctly, closes [#972](https://github.com/lobehub/lobe-chat/issues/972) ([216e700](https://github.com/lobehub/lobe-chat/commit/216e700))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.119.7](https://github.com/lobehub/lobe-chat/compare/v0.119.6...v0.119.7)

<sup>Released on **2024-01-07**</sup>

#### 💄 Styles

- **misc**: Improve share modal style.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve share modal style, closes [#965](https://github.com/lobehub/lobe-chat/issues/965) ([62c0573](https://github.com/lobehub/lobe-chat/commit/62c0573))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.119.6](https://github.com/lobehub/lobe-chat/compare/v0.119.5...v0.119.6)

<sup>Released on **2024-01-06**</sup>

#### 💄 Styles

- **misc**: Improve conversation style.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve conversation style, closes [#962](https://github.com/lobehub/lobe-chat/issues/962) ([b9cc862](https://github.com/lobehub/lobe-chat/commit/b9cc862))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.119.5](https://github.com/lobehub/lobe-chat/compare/v0.119.4...v0.119.5)

<sup>Released on **2024-01-06**</sup>

#### 💄 Styles

- **misc**: Fix topic i18n.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix topic i18n, closes [#961](https://github.com/lobehub/lobe-chat/issues/961) ([4e9ebe2](https://github.com/lobehub/lobe-chat/commit/4e9ebe2))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.119.4](https://github.com/lobehub/lobe-chat/compare/v0.119.3...v0.119.4)

<sup>Released on **2024-01-06**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor to virtual list with react-virtuoso.

#### 🐛 Bug Fixes

- **misc**: Fix auto scroll error and BackBottom error.

#### 💄 Styles

- **misc**: Fix BackBottom zIndex, improve chat list on mobile, improve chat list scrolling to bottom at initial render, improve custom model input, improve topic scroll.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor to virtual list with react-virtuoso ([d6d63b2](https://github.com/lobehub/lobe-chat/commit/d6d63b2))

#### What's fixed

- **misc**: Fix auto scroll error and BackBottom error ([6100970](https://github.com/lobehub/lobe-chat/commit/6100970))

#### Styles

- **misc**: Fix BackBottom zIndex ([254cc54](https://github.com/lobehub/lobe-chat/commit/254cc54))
- **misc**: Improve chat list on mobile ([a894fc5](https://github.com/lobehub/lobe-chat/commit/a894fc5))
- **misc**: Improve chat list scrolling to bottom at initial render ([476304b](https://github.com/lobehub/lobe-chat/commit/476304b))
- **misc**: Improve custom model input ([1c4722f](https://github.com/lobehub/lobe-chat/commit/1c4722f))
- **misc**: Improve topic scroll ([8daf3ac](https://github.com/lobehub/lobe-chat/commit/8daf3ac))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.119.3](https://github.com/lobehub/lobe-chat/compare/v0.119.2...v0.119.3)

<sup>Released on **2024-01-06**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix deploy error. Changed SquareAsterisk to AsteriskSquare.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix deploy error. Changed SquareAsterisk to AsteriskSquare, closes [#952](https://github.com/lobehub/lobe-chat/issues/952) ([61cbcf1](https://github.com/lobehub/lobe-chat/commit/61cbcf1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.119.2](https://github.com/lobehub/lobe-chat/compare/v0.119.1...v0.119.2)

<sup>Released on **2024-01-05**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix function call error with smooth animation.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix function call error with smooth animation, closes [#946](https://github.com/lobehub/lobe-chat/issues/946) ([7242aee](https://github.com/lobehub/lobe-chat/commit/7242aee))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.119.1](https://github.com/lobehub/lobe-chat/compare/v0.119.0...v0.119.1)

<sup>Released on **2024-01-05**</sup>

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>
</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.119.0](https://github.com/lobehub/lobe-chat/compare/v0.118.10...v0.119.0)

<sup>Released on **2024-01-04**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the ChatList.

#### ✨ Features

- **misc**: Support auto rename topic, support delete and regenerate message, support duplicate session, support duplicate topic.

#### 🐛 Bug Fixes

- **misc**: Fix can't uninstall custom plugin in custom plugin modal.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the ChatList ([20f21de](https://github.com/lobehub/lobe-chat/commit/20f21de))

#### What's improved

- **misc**: Support auto rename topic ([4c5a345](https://github.com/lobehub/lobe-chat/commit/4c5a345))
- **misc**: Support delete and regenerate message ([a05be1c](https://github.com/lobehub/lobe-chat/commit/a05be1c))
- **misc**: Support duplicate session ([7a1e011](https://github.com/lobehub/lobe-chat/commit/7a1e011))
- **misc**: Support duplicate topic ([ecf3e5a](https://github.com/lobehub/lobe-chat/commit/ecf3e5a))

#### What's fixed

- **misc**: Fix can't uninstall custom plugin in custom plugin modal ([acae827](https://github.com/lobehub/lobe-chat/commit/acae827))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.118.10](https://github.com/lobehub/lobe-chat/compare/v0.118.9...v0.118.10)

<sup>Released on **2024-01-03**</sup>

#### 🐛 Bug Fixes

- **misc**: Add chat defaultNS.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Add chat defaultNS, closes [#929](https://github.com/lobehub/lobe-chat/issues/929) ([94c2aa1](https://github.com/lobehub/lobe-chat/commit/94c2aa1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.118.9](https://github.com/lobehub/lobe-chat/compare/v0.118.8...v0.118.9)

<sup>Released on **2024-01-03**</sup>

#### 💄 Styles

- **misc**: Add leaving protect alert.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add leaving protect alert, closes [#927](https://github.com/lobehub/lobe-chat/issues/927) ([ea1d0c0](https://github.com/lobehub/lobe-chat/commit/ea1d0c0))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.118.8](https://github.com/lobehub/lobe-chat/compare/v0.118.7...v0.118.8)

<sup>Released on **2024-01-03**</sup>

#### 💄 Styles

- **misc**: Add Vietnamese files and add the vi-VN option in the General Settings.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add Vietnamese files and add the vi-VN option in the General Settings, closes [#860](https://github.com/lobehub/lobe-chat/issues/860) ([c2e5606](https://github.com/lobehub/lobe-chat/commit/c2e5606))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.118.7](https://github.com/lobehub/lobe-chat/compare/v0.118.6...v0.118.7)

<sup>Released on **2024-01-03**</sup>

#### 🐛 Bug Fixes

- **misc**: Desensitize openai base url in the error response.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Desensitize openai base url in the error response, closes [#918](https://github.com/lobehub/lobe-chat/issues/918) ([ab0aeb7](https://github.com/lobehub/lobe-chat/commit/ab0aeb7))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.118.6](https://github.com/lobehub/lobe-chat/compare/v0.118.5...v0.118.6)

<sup>Released on **2024-01-03**</sup>

#### ♻ Code Refactoring

- **misc**: Migration the ChatList into Conversation.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Migration the ChatList into Conversation, closes [#916](https://github.com/lobehub/lobe-chat/issues/916) ([6ee3795](https://github.com/lobehub/lobe-chat/commit/6ee3795))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.118.5](https://github.com/lobehub/lobe-chat/compare/v0.118.4...v0.118.5)

<sup>Released on **2024-01-02**</sup>

#### 🐛 Bug Fixes

- **misc**: Mobile device return to the previous page error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Mobile device return to the previous page error, closes [#886](https://github.com/lobehub/lobe-chat/issues/886) ([99cfc0f](https://github.com/lobehub/lobe-chat/commit/99cfc0f))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.118.4](https://github.com/lobehub/lobe-chat/compare/v0.118.3...v0.118.4)

<sup>Released on **2024-01-02**</sup>

#### 🐛 Bug Fixes

- **misc**: Update dalle identifier to fix unstable dalle function call.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Update dalle identifier to fix unstable dalle function call, closes [#896](https://github.com/lobehub/lobe-chat/issues/896) ([9d9ac32](https://github.com/lobehub/lobe-chat/commit/9d9ac32))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.118.3](https://github.com/lobehub/lobe-chat/compare/v0.118.2...v0.118.3)

<sup>Released on **2024-01-01**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix parse error of tool calls at end.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix parse error of tool calls at end, closes [#893](https://github.com/lobehub/lobe-chat/issues/893) ([f369b6e](https://github.com/lobehub/lobe-chat/commit/f369b6e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.118.2](https://github.com/lobehub/lobe-chat/compare/v0.118.1...v0.118.2)

<sup>Released on **2023-12-31**</sup>

#### 🐛 Bug Fixes

- **misc**: Pin antd version to fix chat page crash.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Pin antd version to fix chat page crash, closes [#884](https://github.com/lobehub/lobe-chat/issues/884) ([31484ff](https://github.com/lobehub/lobe-chat/commit/31484ff))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.118.1](https://github.com/lobehub/lobe-chat/compare/v0.118.0...v0.118.1)

<sup>Released on **2023-12-30**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix dalle image download error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix dalle image download error, closes [#868](https://github.com/lobehub/lobe-chat/issues/868) ([5b6d11f](https://github.com/lobehub/lobe-chat/commit/5b6d11f))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.118.0](https://github.com/lobehub/lobe-chat/compare/v0.117.5...v0.118.0)

<sup>Released on **2023-12-29**</sup>

#### ✨ Features

- **misc**: Support markdown type plugin.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support markdown type plugin, closes [#865](https://github.com/lobehub/lobe-chat/issues/865) ([2791166](https://github.com/lobehub/lobe-chat/commit/2791166))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.117.5](https://github.com/lobehub/lobe-chat/compare/v0.117.4...v0.117.5)

<sup>Released on **2023-12-29**</sup>

#### 🐛 Bug Fixes

- **misc**: The input box is prone to losing focus.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: The input box is prone to losing focus, closes [#834](https://github.com/lobehub/lobe-chat/issues/834) ([26a42f6](https://github.com/lobehub/lobe-chat/commit/26a42f6))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.117.4](https://github.com/lobehub/lobe-chat/compare/v0.117.3...v0.117.4)

<sup>Released on **2023-12-28**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix messages not refresh when creating a new topic.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix messages not refresh when creating a new topic, closes [#856](https://github.com/lobehub/lobe-chat/issues/856) ([5e7985a](https://github.com/lobehub/lobe-chat/commit/5e7985a))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.117.3](https://github.com/lobehub/lobe-chat/compare/v0.117.2...v0.117.3)

<sup>Released on **2023-12-28**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix tool calls at end, fix vision model max tokens, improve vision model checker.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix tool calls at end ([b0b615a](https://github.com/lobehub/lobe-chat/commit/b0b615a))
- **misc**: Fix vision model max tokens ([8b704a0](https://github.com/lobehub/lobe-chat/commit/8b704a0))
- **misc**: Improve vision model checker ([da7d177](https://github.com/lobehub/lobe-chat/commit/da7d177))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.117.2](https://github.com/lobehub/lobe-chat/compare/v0.117.1...v0.117.2)

<sup>Released on **2023-12-28**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix market locale missing.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix market locale missing, closes [#851](https://github.com/lobehub/lobe-chat/issues/851) ([e23ec66](https://github.com/lobehub/lobe-chat/commit/e23ec66))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.117.1](https://github.com/lobehub/lobe-chat/compare/v0.117.0...v0.117.1)

<sup>Released on **2023-12-27**</sup>

#### 💄 Styles

- **misc**: Add image download functionality to DALL·E render component.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add image download functionality to DALL·E render component, closes [#778](https://github.com/lobehub/lobe-chat/issues/778) ([31b8047](https://github.com/lobehub/lobe-chat/commit/31b8047))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.117.0](https://github.com/lobehub/lobe-chat/compare/v0.116.5...v0.117.0)

<sup>Released on **2023-12-27**</sup>

#### ✨ Features

- **misc**: Support plugin settings env.

#### 🐛 Bug Fixes

- **misc**: Improve topic search experience.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support plugin settings env, closes [#821](https://github.com/lobehub/lobe-chat/issues/821) ([efd9dc9](https://github.com/lobehub/lobe-chat/commit/efd9dc9))

#### What's fixed

- **misc**: Improve topic search experience, closes [#828](https://github.com/lobehub/lobe-chat/issues/828) ([ad55f1c](https://github.com/lobehub/lobe-chat/commit/ad55f1c))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.116.5](https://github.com/lobehub/lobe-chat/compare/v0.116.4...v0.116.5)

<sup>Released on **2023-12-27**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix input box losing focus after sending a message on the desktop.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix input box losing focus after sending a message on the desktop, closes [#830](https://github.com/lobehub/lobe-chat/issues/830) ([d491af9](https://github.com/lobehub/lobe-chat/commit/d491af9))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.116.4](https://github.com/lobehub/lobe-chat/compare/v0.116.3...v0.116.4)

<sup>Released on **2023-12-26**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix ShareModal.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix ShareModal ([4592515](https://github.com/lobehub/lobe-chat/commit/4592515))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.116.3](https://github.com/lobehub/lobe-chat/compare/v0.116.2...v0.116.3)

<sup>Released on **2023-12-26**</sup>

#### 💄 Styles

- **misc**: Fix typo.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix typo ([9d329a9](https://github.com/lobehub/lobe-chat/commit/9d329a9))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.116.2](https://github.com/lobehub/lobe-chat/compare/v0.116.1...v0.116.2)

<sup>Released on **2023-12-26**</sup>

#### 💄 Styles

- **misc**: Update Modal style.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update Modal style ([ac7d309](https://github.com/lobehub/lobe-chat/commit/ac7d309))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.116.1](https://github.com/lobehub/lobe-chat/compare/v0.116.0...v0.116.1)

<sup>Released on **2023-12-26**</sup>

#### 💄 Styles

- **misc**: Support slider and select plugin setting render.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Support slider and select plugin setting render, closes [#819](https://github.com/lobehub/lobe-chat/issues/819) ([3190b44](https://github.com/lobehub/lobe-chat/commit/3190b44))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.116.0](https://github.com/lobehub/lobe-chat/compare/v0.115.13...v0.116.0)

<sup>Released on **2023-12-26**</sup>

#### ✨ Features

- **misc**: Support OpenAI tool calls.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support OpenAI tool calls ([9681fdc](https://github.com/lobehub/lobe-chat/commit/9681fdc))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.115.13](https://github.com/lobehub/lobe-chat/compare/v0.115.12...v0.115.13)

<sup>Released on **2023-12-26**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix remove tts and translate not working.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix remove tts and translate not working, closes [#818](https://github.com/lobehub/lobe-chat/issues/818) ([4a275e9](https://github.com/lobehub/lobe-chat/commit/4a275e9))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.115.12](https://github.com/lobehub/lobe-chat/compare/v0.115.11...v0.115.12)

<sup>Released on **2023-12-25**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix active setting tab after click agent setting button.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix active setting tab after click agent setting button ([c480fa9](https://github.com/lobehub/lobe-chat/commit/c480fa9))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.115.11](https://github.com/lobehub/lobe-chat/compare/v0.115.10...v0.115.11)

<sup>Released on **2023-12-25**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix agent system role modal scrolling when content is too long.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix agent system role modal scrolling when content is too long, closes [#801](https://github.com/lobehub/lobe-chat/issues/801) ([f482a80](https://github.com/lobehub/lobe-chat/commit/f482a80))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.115.10](https://github.com/lobehub/lobe-chat/compare/v0.115.9...v0.115.10)

<sup>Released on **2023-12-25**</sup>

#### 💄 Styles

- **misc**: Fix some style problem.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix some style problem ([447c006](https://github.com/lobehub/lobe-chat/commit/447c006))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.115.9](https://github.com/lobehub/lobe-chat/compare/v0.115.8...v0.115.9)

<sup>Released on **2023-12-24**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix `PLUGINS_INDEX_URL` not working, fix a translation error in Traditional Chinese.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix `PLUGINS_INDEX_URL` not working, closes [#793](https://github.com/lobehub/lobe-chat/issues/793) ([152913e](https://github.com/lobehub/lobe-chat/commit/152913e))
- **misc**: Fix a translation error in Traditional Chinese, closes [#789](https://github.com/lobehub/lobe-chat/issues/789) ([80c02ee](https://github.com/lobehub/lobe-chat/commit/80c02ee))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.115.8](https://github.com/lobehub/lobe-chat/compare/v0.115.7...v0.115.8)

<sup>Released on **2023-12-24**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix CUSTOM_MODEL `-` operator not working.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix CUSTOM_MODEL `-` operator not working, closes [#791](https://github.com/lobehub/lobe-chat/issues/791) ([26b968f](https://github.com/lobehub/lobe-chat/commit/26b968f))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.115.7](https://github.com/lobehub/lobe-chat/compare/v0.115.6...v0.115.7)

<sup>Released on **2023-12-23**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix auto scrolling when generating message.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix auto scrolling when generating message, closes [#785](https://github.com/lobehub/lobe-chat/issues/785) ([1a236e6](https://github.com/lobehub/lobe-chat/commit/1a236e6))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.115.6](https://github.com/lobehub/lobe-chat/compare/v0.115.5...v0.115.6)

<sup>Released on **2023-12-23**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix maxTokens params still work when disable enableMaxTokens.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix maxTokens params still work when disable enableMaxTokens, closes [#779](https://github.com/lobehub/lobe-chat/issues/779) ([20956ea](https://github.com/lobehub/lobe-chat/commit/20956ea))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.115.5](https://github.com/lobehub/lobe-chat/compare/v0.115.4...v0.115.5)

<sup>Released on **2023-12-23**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix image display error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix image display error, closes [#777](https://github.com/lobehub/lobe-chat/issues/777) ([08659d6](https://github.com/lobehub/lobe-chat/commit/08659d6))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.115.4](https://github.com/lobehub/lobe-chat/compare/v0.115.3...v0.115.4)

<sup>Released on **2023-12-23**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the ChatMessage type.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the ChatMessage type ([40375bd](https://github.com/lobehub/lobe-chat/commit/40375bd))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.115.3](https://github.com/lobehub/lobe-chat/compare/v0.115.2...v0.115.3)

<sup>Released on **2023-12-23**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor and clean global store and chat store.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor and clean global store and chat store, closes [#774](https://github.com/lobehub/lobe-chat/issues/774) ([6120042](https://github.com/lobehub/lobe-chat/commit/6120042))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.115.2](https://github.com/lobehub/lobe-chat/compare/v0.115.1...v0.115.2)

<sup>Released on **2023-12-23**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix envs like `CUSTOM_MODELS` don't work with docker deployment.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix envs like `CUSTOM_MODELS` don't work with docker deployment, closes [#773](https://github.com/lobehub/lobe-chat/issues/773) ([54dc18b](https://github.com/lobehub/lobe-chat/commit/54dc18b))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.115.1](https://github.com/lobehub/lobe-chat/compare/v0.115.0...v0.115.1)

<sup>Released on **2023-12-22**</sup>

#### 💄 Styles

- **misc**: Lock ui version to fix setting form style.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Lock ui version to fix setting form style ([6cdf548](https://github.com/lobehub/lobe-chat/commit/6cdf548))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.115.0](https://github.com/lobehub/lobe-chat/compare/v0.114.9...v0.115.0)

<sup>Released on **2023-12-22**</sup>

#### ✨ Features

- **misc**: Support Dall·E 3.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support Dall·E 3, closes [#697](https://github.com/lobehub/lobe-chat/issues/697) ([e39d199](https://github.com/lobehub/lobe-chat/commit/e39d199))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.114.9](https://github.com/lobehub/lobe-chat/compare/v0.114.8...v0.114.9)

<sup>Released on **2023-12-22**</sup>

#### 💄 Styles

- **misc**: Support it-IT nl-NL and pl-PL locales.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Support it-IT nl-NL and pl-PL locales, closes [#759](https://github.com/lobehub/lobe-chat/issues/759) ([e49817c](https://github.com/lobehub/lobe-chat/commit/e49817c))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.114.8](https://github.com/lobehub/lobe-chat/compare/v0.114.7...v0.114.8)

<sup>Released on **2023-12-22**</sup>

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>
</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.114.7](https://github.com/lobehub/lobe-chat/compare/v0.114.6...v0.114.7)

<sup>Released on **2023-12-22**</sup>

#### ♻ Code Refactoring

- **misc**: Move the conversation and chatInput to features folder.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Move the conversation and chatInput to features folder, closes [#750](https://github.com/lobehub/lobe-chat/issues/750) ([0334592](https://github.com/lobehub/lobe-chat/commit/0334592))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.114.6](https://github.com/lobehub/lobe-chat/compare/v0.114.5...v0.114.6)

<sup>Released on **2023-12-22**</sup>

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>
</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.114.5](https://github.com/lobehub/lobe-chat/compare/v0.114.4...v0.114.5)

<sup>Released on **2023-12-19**</sup>

#### 💄 Styles

- **misc**: Fix plugin iframe width.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix plugin iframe width, closes [#721](https://github.com/lobehub/lobe-chat/issues/721) ([53ad132](https://github.com/lobehub/lobe-chat/commit/53ad132))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.114.4](https://github.com/lobehub/lobe-chat/compare/v0.114.3...v0.114.4)

<sup>Released on **2023-12-19**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix agent system role modal scrolling when content is too long.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix agent system role modal scrolling when content is too long, closes [#716](https://github.com/lobehub/lobe-chat/issues/716) ([c3e36d1](https://github.com/lobehub/lobe-chat/commit/c3e36d1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.114.3](https://github.com/lobehub/lobe-chat/compare/v0.114.2...v0.114.3)

<sup>Released on **2023-12-18**</sup>

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>
</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.114.2](https://github.com/lobehub/lobe-chat/compare/v0.114.1...v0.114.2)

<sup>Released on **2023-12-17**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix chat error when message has image with non-vision model.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix chat error when message has image with non-vision model, closes [#698](https://github.com/lobehub/lobe-chat/issues/698) [#693](https://github.com/lobehub/lobe-chat/issues/693) ([b142c17](https://github.com/lobehub/lobe-chat/commit/b142c17))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.114.1](https://github.com/lobehub/lobe-chat/compare/v0.114.0...v0.114.1)

<sup>Released on **2023-12-16**</sup>

#### 🐛 Bug Fixes

- **misc**: Inject tool description into agent system role.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Inject tool description into agent system role, closes [#681](https://github.com/lobehub/lobe-chat/issues/681) ([e7a8cff](https://github.com/lobehub/lobe-chat/commit/e7a8cff))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.114.0](https://github.com/lobehub/lobe-chat/compare/v0.113.1...v0.114.0)

<sup>Released on **2023-12-16**</sup>

#### ✨ Features

- **misc**: Supports setting multiple access code.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Supports setting multiple access code, closes [#647](https://github.com/lobehub/lobe-chat/issues/647) ([7db0430](https://github.com/lobehub/lobe-chat/commit/7db0430))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.113.1](https://github.com/lobehub/lobe-chat/compare/v0.113.0...v0.113.1)

<sup>Released on **2023-12-16**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix fontsize setting and audio download style.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix fontsize setting and audio download style, closes [#433](https://github.com/lobehub/lobe-chat/issues/433) ([6882752](https://github.com/lobehub/lobe-chat/commit/6882752))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.113.0](https://github.com/lobehub/lobe-chat/compare/v0.112.1...v0.113.0)

<sup>Released on **2023-12-16**</sup>

#### ✨ Features

- **locale**: Add es-ES pt-BR de-DE tr-TR.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **locale**: Add es-ES pt-BR de-DE tr-TR, closes [#659](https://github.com/lobehub/lobe-chat/issues/659) ([021abfa](https://github.com/lobehub/lobe-chat/commit/021abfa))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.112.1](https://github.com/lobehub/lobe-chat/compare/v0.112.0...v0.112.1)

<sup>Released on **2023-12-16**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix locales.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix locales ([4384dc2](https://github.com/lobehub/lobe-chat/commit/4384dc2))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.112.0](https://github.com/lobehub/lobe-chat/compare/v0.111.6...v0.112.0)

<sup>Released on **2023-12-16**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor global and share service, refactor plugin dev modal and improve plugin store, refactor with OpenAPIConvertor.

#### ✨ Features

- **misc**: Introduce plugin detail modal, support OpenAI plugin manifest, support OpenAPI Authentication, support OpenAPI schema in lobe plugin, support parse openapi schema.

#### 🐛 Bug Fixes

- **misc**: Fix function apiName length, try with node mode plugins.

#### 💄 Styles

- **misc**: Fix function message style, fix mobile padding of plugin dev modal, improve settings display, Update tool style.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor global and share service ([dd6f00e](https://github.com/lobehub/lobe-chat/commit/dd6f00e))
- **misc**: Refactor plugin dev modal and improve plugin store ([4dc5e35](https://github.com/lobehub/lobe-chat/commit/4dc5e35))
- **misc**: Refactor with OpenAPIConvertor ([605b3bf](https://github.com/lobehub/lobe-chat/commit/605b3bf))

#### What's improved

- **misc**: Introduce plugin detail modal ([0308783](https://github.com/lobehub/lobe-chat/commit/0308783))
- **misc**: Support OpenAI plugin manifest ([04ff2d5](https://github.com/lobehub/lobe-chat/commit/04ff2d5))
- **misc**: Support OpenAPI Authentication ([820c15e](https://github.com/lobehub/lobe-chat/commit/820c15e))
- **misc**: Support OpenAPI schema in lobe plugin, closes [#614](https://github.com/lobehub/lobe-chat/issues/614) ([5426a54](https://github.com/lobehub/lobe-chat/commit/5426a54))
- **misc**: Support parse openapi schema ([11a39b1](https://github.com/lobehub/lobe-chat/commit/11a39b1))

#### What's fixed

- **misc**: Fix function apiName length ([b6f8c16](https://github.com/lobehub/lobe-chat/commit/b6f8c16))
- **misc**: Try with node mode plugins ([6bb547f](https://github.com/lobehub/lobe-chat/commit/6bb547f))

#### Styles

- **misc**: Fix function message style ([4fee0b1](https://github.com/lobehub/lobe-chat/commit/4fee0b1))
- **misc**: Fix mobile padding of plugin dev modal ([7f7070a](https://github.com/lobehub/lobe-chat/commit/7f7070a))
- **misc**: Improve settings display ([df57cde](https://github.com/lobehub/lobe-chat/commit/df57cde))
- **misc**: Update tool style ([292a3e1](https://github.com/lobehub/lobe-chat/commit/292a3e1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.111.6](https://github.com/lobehub/lobe-chat/compare/v0.111.5...v0.111.6)

<sup>Released on **2023-12-15**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix deployment build failure.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix deployment build failure, closes [#672](https://github.com/lobehub/lobe-chat/issues/672) ([3878dcd](https://github.com/lobehub/lobe-chat/commit/3878dcd))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.111.5](https://github.com/lobehub/lobe-chat/compare/v0.111.4...v0.111.5)

<sup>Released on **2023-12-14**</sup>

#### 🐛 Bug Fixes

- **misc**: Wrong locale language in en_US.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Wrong locale language in en_US, closes [#660](https://github.com/lobehub/lobe-chat/issues/660) ([e1c31ee](https://github.com/lobehub/lobe-chat/commit/e1c31ee))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.111.4](https://github.com/lobehub/lobe-chat/compare/v0.111.3...v0.111.4)

<sup>Released on **2023-12-14**</sup>

#### 🐛 Bug Fixes

- **misc**: Revert "🐛 fix: clean up gpt-3.5 model".

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Revert "🐛 fix: clean up gpt-3.5 model", closes [#653](https://github.com/lobehub/lobe-chat/issues/653) ([b8b14fc](https://github.com/lobehub/lobe-chat/commit/b8b14fc))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.111.3](https://github.com/lobehub/lobe-chat/compare/v0.111.2...v0.111.3)

<sup>Released on **2023-12-14**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix the history-count text.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix the history-count text, closes [#615](https://github.com/lobehub/lobe-chat/issues/615) ([4db1cef](https://github.com/lobehub/lobe-chat/commit/4db1cef))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.111.2](https://github.com/lobehub/lobe-chat/compare/v0.111.1...v0.111.2)

<sup>Released on **2023-12-13**</sup>

#### 🐛 Bug Fixes

- **misc**: Change topic-deletion hotkey.

#### 💄 Styles

- **misc**: Fix image display in safari (fix.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Change topic-deletion hotkey, closes [#616](https://github.com/lobehub/lobe-chat/issues/616) ([912ff45](https://github.com/lobehub/lobe-chat/commit/912ff45))

#### Styles

- **misc**: Fix image display in safari (fix, closes [#571](https://github.com/lobehub/lobe-chat/issues/571) ([4beefa7](https://github.com/lobehub/lobe-chat/commit/4beefa7))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.111.1](https://github.com/lobehub/lobe-chat/compare/v0.111.0...v0.111.1)

<sup>Released on **2023-12-13**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix locale typo.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix locale typo ([f44b41e](https://github.com/lobehub/lobe-chat/commit/f44b41e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.111.0](https://github.com/lobehub/lobe-chat/compare/v0.110.10...v0.111.0)

<sup>Released on **2023-12-13**</sup>

#### ✨ Features

- **locale**: Add fr-FR.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **locale**: Add fr-FR, closes [#637](https://github.com/lobehub/lobe-chat/issues/637) ([357141c](https://github.com/lobehub/lobe-chat/commit/357141c))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.110.10](https://github.com/lobehub/lobe-chat/compare/v0.110.9...v0.110.10)

<sup>Released on **2023-12-13**</sup>

#### 🐛 Bug Fixes

- **misc**: Add cancel button text i18n for delete assistant modal.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Add cancel button text i18n for delete assistant modal, closes [#640](https://github.com/lobehub/lobe-chat/issues/640) ([fae04c9](https://github.com/lobehub/lobe-chat/commit/fae04c9))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.110.9](https://github.com/lobehub/lobe-chat/compare/v0.110.8...v0.110.9)

<sup>Released on **2023-12-13**</sup>

#### 🐛 Bug Fixes

- **misc**: ChatInput should have maxHeight.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: ChatInput should have maxHeight, closes [#630](https://github.com/lobehub/lobe-chat/issues/630) ([8dfe1b8](https://github.com/lobehub/lobe-chat/commit/8dfe1b8))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.110.8](https://github.com/lobehub/lobe-chat/compare/v0.110.7...v0.110.8)

<sup>Released on **2023-12-12**</sup>

#### 🐛 Bug Fixes

- **misc**: Clean up gpt-3.5 model.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Clean up gpt-3.5 model, closes [#554](https://github.com/lobehub/lobe-chat/issues/554) ([9616783](https://github.com/lobehub/lobe-chat/commit/9616783))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.110.7](https://github.com/lobehub/lobe-chat/compare/v0.110.6...v0.110.7)

<sup>Released on **2023-12-11**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix language settings may not take effect.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix language settings may not take effect, closes [#595](https://github.com/lobehub/lobe-chat/issues/595) ([a5db64e](https://github.com/lobehub/lobe-chat/commit/a5db64e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.110.6](https://github.com/lobehub/lobe-chat/compare/v0.110.5...v0.110.6)

<sup>Released on **2023-12-11**</sup>

#### 🐛 Bug Fixes

- **misc**: Sharp missing in docker production.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Sharp missing in docker production, closes [#603](https://github.com/lobehub/lobe-chat/issues/603) ([d89b48d](https://github.com/lobehub/lobe-chat/commit/d89b48d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.110.5](https://github.com/lobehub/lobe-chat/compare/v0.110.4...v0.110.5)

<sup>Released on **2023-12-10**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix setting plugin i18n.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix setting plugin i18n, closes [#606](https://github.com/lobehub/lobe-chat/issues/606) ([4e18ebf](https://github.com/lobehub/lobe-chat/commit/4e18ebf))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.110.4](https://github.com/lobehub/lobe-chat/compare/v0.110.3...v0.110.4)

<sup>Released on **2023-12-08**</sup>

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>
</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.110.3](https://github.com/lobehub/lobe-chat/compare/v0.110.2...v0.110.3)

<sup>Released on **2023-12-08**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor with new plugin implement with dexie db.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor with new plugin implement with dexie db, closes [#596](https://github.com/lobehub/lobe-chat/issues/596) ([f3b5e7b](https://github.com/lobehub/lobe-chat/commit/f3b5e7b))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.110.2](https://github.com/lobehub/lobe-chat/compare/v0.110.1...v0.110.2)

<sup>Released on **2023-12-08**</sup>

#### 💄 Styles

- **misc**: Fix ChatInputArea style and typo (resolve.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix ChatInputArea style and typo (resolve, closes [#599](https://github.com/lobehub/lobe-chat/issues/599) ([0d08f3b](https://github.com/lobehub/lobe-chat/commit/0d08f3b))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.110.1](https://github.com/lobehub/lobe-chat/compare/v0.110.0...v0.110.1)

<sup>Released on **2023-12-08**</sup>

#### 🐛 Bug Fixes

- **misc**: Sharp missing in production.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Sharp missing in production, closes [#598](https://github.com/lobehub/lobe-chat/issues/598) ([c8ef782](https://github.com/lobehub/lobe-chat/commit/c8ef782))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.110.0](https://github.com/lobehub/lobe-chat/compare/v0.109.1...v0.110.0)

<sup>Released on **2023-12-07**</sup>

#### ✨ Features

- **misc**: Local TTS Player.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Local TTS Player, closes [#587](https://github.com/lobehub/lobe-chat/issues/587) ([87b51bd](https://github.com/lobehub/lobe-chat/commit/87b51bd))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.109.1](https://github.com/lobehub/lobe-chat/compare/v0.109.0...v0.109.1)

<sup>Released on **2023-12-07**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix agent settings crash with old pluginManifest.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix agent settings crash with old pluginManifest ([8b80dfd](https://github.com/lobehub/lobe-chat/commit/8b80dfd))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.109.0](https://github.com/lobehub/lobe-chat/compare/v0.108.0...v0.109.0)

<sup>Released on **2023-12-06**</sup>

#### ✨ Features

- **misc**: Introducing plugin store and refactor with tool concept.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Introducing plugin store and refactor with tool concept, closes [#573](https://github.com/lobehub/lobe-chat/issues/573) ([92f43d1](https://github.com/lobehub/lobe-chat/commit/92f43d1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.108.0](https://github.com/lobehub/lobe-chat/compare/v0.107.16...v0.108.0)

<sup>Released on **2023-12-03**</sup>

#### ✨ Features

- **misc**: Hide the password form item in the settings when there is no `ACCESS_CODE` env.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Hide the password form item in the settings when there is no `ACCESS_CODE` env, closes [#568](https://github.com/lobehub/lobe-chat/issues/568) ([3b5f8b2](https://github.com/lobehub/lobe-chat/commit/3b5f8b2))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.16](https://github.com/lobehub/lobe-chat/compare/v0.107.15...v0.107.16)

<sup>Released on **2023-12-03**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix custom agent meta issue.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix custom agent meta issue, closes [#567](https://github.com/lobehub/lobe-chat/issues/567) ([75560e1](https://github.com/lobehub/lobe-chat/commit/75560e1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.15](https://github.com/lobehub/lobe-chat/compare/v0.107.14...v0.107.15)

<sup>Released on **2023-12-03**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix messages flickering when creating topic.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix messages flickering when creating topic, closes [#565](https://github.com/lobehub/lobe-chat/issues/565) ([7127550](https://github.com/lobehub/lobe-chat/commit/7127550))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.14](https://github.com/lobehub/lobe-chat/compare/v0.107.13...v0.107.14)

<sup>Released on **2023-12-03**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix opt+delete fail in inputing (resolve.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix opt+delete fail in inputing (resolve, closes [#556](https://github.com/lobehub/lobe-chat/issues/556) ([4d5d93d](https://github.com/lobehub/lobe-chat/commit/4d5d93d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.13](https://github.com/lobehub/lobe-chat/compare/v0.107.12...v0.107.13)

<sup>Released on **2023-12-03**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor Image components.

#### 🐛 Bug Fixes

- **misc**: Fix a bug that can't send only images with empty content, Fix image gallery sort index, Fix image gallery sort index, Fix image sort index, Fix image upload error, Fix import.

#### 💄 Styles

- **misc**: Change image fit to cover mode, Fix empty files style, Move file inside chat input in mobile mode, Update editable image style, Update image default background color, Update image editable style, Update image grid, Update Image grid, Update image remove button hover style.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor Image components ([72dcd18](https://github.com/lobehub/lobe-chat/commit/72dcd18))

#### What's fixed

- **misc**: Fix a bug that can't send only images with empty content ([9601520](https://github.com/lobehub/lobe-chat/commit/9601520))
- **misc**: Fix image gallery sort index ([16548d3](https://github.com/lobehub/lobe-chat/commit/16548d3))
- **misc**: Fix image gallery sort index ([5636599](https://github.com/lobehub/lobe-chat/commit/5636599))
- **misc**: Fix image sort index ([29cf223](https://github.com/lobehub/lobe-chat/commit/29cf223))
- **misc**: Fix image upload error ([c7745c7](https://github.com/lobehub/lobe-chat/commit/c7745c7))
- **misc**: Fix import ([af797d6](https://github.com/lobehub/lobe-chat/commit/af797d6))

#### Styles

- **misc**: Change image fit to cover mode ([9fdc459](https://github.com/lobehub/lobe-chat/commit/9fdc459))
- **misc**: Fix empty files style ([bbe14c4](https://github.com/lobehub/lobe-chat/commit/bbe14c4))
- **misc**: Move file inside chat input in mobile mode ([b6401c1](https://github.com/lobehub/lobe-chat/commit/b6401c1))
- **misc**: Update editable image style ([a96ca4c](https://github.com/lobehub/lobe-chat/commit/a96ca4c))
- **misc**: Update image default background color ([fadc024](https://github.com/lobehub/lobe-chat/commit/fadc024))
- **misc**: Update image editable style ([8dea792](https://github.com/lobehub/lobe-chat/commit/8dea792))
- **misc**: Update image grid ([901d10c](https://github.com/lobehub/lobe-chat/commit/901d10c))
- **misc**: Update Image grid ([c68788d](https://github.com/lobehub/lobe-chat/commit/c68788d))
- **misc**: Update image remove button hover style ([5bc916c](https://github.com/lobehub/lobe-chat/commit/5bc916c))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.12](https://github.com/lobehub/lobe-chat/compare/v0.107.11...v0.107.12)

<sup>Released on **2023-12-02**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix topic not refresh when switching sessions quickly.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix topic not refresh when switching sessions quickly, closes [#555](https://github.com/lobehub/lobe-chat/issues/555) ([1806c05](https://github.com/lobehub/lobe-chat/commit/1806c05))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.11](https://github.com/lobehub/lobe-chat/compare/v0.107.10...v0.107.11)

<sup>Released on **2023-12-01**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix switch model don't work on mobile.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix switch model don't work on mobile, closes [#541](https://github.com/lobehub/lobe-chat/issues/541) ([609f505](https://github.com/lobehub/lobe-chat/commit/609f505))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.10](https://github.com/lobehub/lobe-chat/compare/v0.107.9...v0.107.10)

<sup>Released on **2023-11-30**</sup>

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>
</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.9](https://github.com/lobehub/lobe-chat/compare/v0.107.8...v0.107.9)

<sup>Released on **2023-11-30**</sup>

#### 🐛 Bug Fixes

- **misc**: Switch session causing problem.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Switch session causing problem, closes [#535](https://github.com/lobehub/lobe-chat/issues/535) ([205bc42](https://github.com/lobehub/lobe-chat/commit/205bc42))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.8](https://github.com/lobehub/lobe-chat/compare/v0.107.7...v0.107.8)

<sup>Released on **2023-11-30**</sup>

#### 💄 Styles

- **misc**: Fix chatitem gap.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix chatitem gap ([772bb7c](https://github.com/lobehub/lobe-chat/commit/772bb7c))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.7](https://github.com/lobehub/lobe-chat/compare/v0.107.6...v0.107.7)

<sup>Released on **2023-11-30**</sup>

#### 🐛 Bug Fixes

- **misc**: Improve plugin message display.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Improve plugin message display ([208200a](https://github.com/lobehub/lobe-chat/commit/208200a))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.6](https://github.com/lobehub/lobe-chat/compare/v0.107.5...v0.107.6)

<sup>Released on **2023-11-30**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正调用插件查询的显示问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正调用插件查询的显示问题 ([671ccef](https://github.com/lobehub/lobe-chat/commit/671ccef))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.5](https://github.com/lobehub/lobe-chat/compare/v0.107.4...v0.107.5)

<sup>Released on **2023-11-30**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正调用插件查询的显示问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正调用插件查询的显示问题 ([1457fe8](https://github.com/lobehub/lobe-chat/commit/1457fe8))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.4](https://github.com/lobehub/lobe-chat/compare/v0.107.3...v0.107.4)

<sup>Released on **2023-11-30**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix a bug that remove all topics when clear message.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix a bug that remove all topics when clear message ([1ab912d](https://github.com/lobehub/lobe-chat/commit/1ab912d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.3](https://github.com/lobehub/lobe-chat/compare/v0.107.2...v0.107.3)

<sup>Released on **2023-11-30**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix a bug that trigger plugin's message type error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix a bug that trigger plugin's message type error ([b9c7849](https://github.com/lobehub/lobe-chat/commit/b9c7849))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.2](https://github.com/lobehub/lobe-chat/compare/v0.107.1...v0.107.2)

<sup>Released on **2023-11-30**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix a bug that export a session without messages.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix a bug that export a session without messages ([8e84f35](https://github.com/lobehub/lobe-chat/commit/8e84f35))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.107.1](https://github.com/lobehub/lobe-chat/compare/v0.107.0...v0.107.1)

<sup>Released on **2023-11-30**</sup>

#### 💄 Styles

- **misc**: 优化文案.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 优化文案 ([aaa03c5](https://github.com/lobehub/lobe-chat/commit/aaa03c5))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.107.0](https://github.com/lobehub/lobe-chat/compare/v0.106.0...v0.107.0)

<sup>Released on **2023-11-30**</sup>

#### ✨ Features

- **misc**: Refactor the persist layer from zustand's persist to dexie ORM.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Refactor the persist layer from zustand's persist to dexie ORM, closes [#500](https://github.com/lobehub/lobe-chat/issues/500) ([9ae3a8e](https://github.com/lobehub/lobe-chat/commit/9ae3a8e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.106.0](https://github.com/lobehub/lobe-chat/compare/v0.105.2...v0.106.0)

<sup>Released on **2023-11-29**</sup>

#### ✨ Features

- **misc**: Support custom deletion, addition, and renaming of models.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support custom deletion, addition, and renaming of models, closes [#521](https://github.com/lobehub/lobe-chat/issues/521) [#518](https://github.com/lobehub/lobe-chat/issues/518) [#518](https://github.com/lobehub/lobe-chat/issues/518) ([65e0824](https://github.com/lobehub/lobe-chat/commit/65e0824))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.105.2](https://github.com/lobehub/lobe-chat/compare/v0.105.1...v0.105.2)

<sup>Released on **2023-11-27**</sup>

#### 🐛 Bug Fixes

- **misc**: Add some env to Dockerfile.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Add some env to Dockerfile, closes [#514](https://github.com/lobehub/lobe-chat/issues/514) ([ed148db](https://github.com/lobehub/lobe-chat/commit/ed148db))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.105.1](https://github.com/lobehub/lobe-chat/compare/v0.105.0...v0.105.1)

<sup>Released on **2023-11-27**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix agent market detail scroll error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix agent market detail scroll error, closes [#503](https://github.com/lobehub/lobe-chat/issues/503) ([8900ad0](https://github.com/lobehub/lobe-chat/commit/8900ad0))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.105.0](https://github.com/lobehub/lobe-chat/compare/v0.104.0...v0.105.0)

<sup>Released on **2023-11-22**</sup>

#### ✨ Features

- **misc**: Standalone pluginn can get more arguments on init.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Standalone pluginn can get more arguments on init, closes [#498](https://github.com/lobehub/lobe-chat/issues/498) ([a7624f5](https://github.com/lobehub/lobe-chat/commit/a7624f5))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.104.0](https://github.com/lobehub/lobe-chat/compare/v0.103.1...v0.104.0)

<sup>Released on **2023-11-21**</sup>

#### ✨ Features

- **misc**: Support using env variable to set regions for OpenAI Edge Functions..

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support using env variable to set regions for OpenAI Edge Functions., closes [#473](https://github.com/lobehub/lobe-chat/issues/473) ([de6b79e](https://github.com/lobehub/lobe-chat/commit/de6b79e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.103.1](https://github.com/lobehub/lobe-chat/compare/v0.103.0...v0.103.1)

<sup>Released on **2023-11-21**</sup>

#### 🐛 Bug Fixes

- **misc**: Image optimization in docker standalone build.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Image optimization in docker standalone build, closes [#494](https://github.com/lobehub/lobe-chat/issues/494) ([d2bcac3](https://github.com/lobehub/lobe-chat/commit/d2bcac3))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.103.0](https://github.com/lobehub/lobe-chat/compare/v0.102.4...v0.103.0)

<sup>Released on **2023-11-20**</sup>

#### ✨ Features

- **misc**: Support the auto create topic configuration.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support the auto create topic configuration, closes [#490](https://github.com/lobehub/lobe-chat/issues/490) ([a7b7ef0](https://github.com/lobehub/lobe-chat/commit/a7b7ef0))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.102.4](https://github.com/lobehub/lobe-chat/compare/v0.102.3...v0.102.4)

<sup>Released on **2023-11-20**</sup>

#### 🐛 Bug Fixes

- **plugin**: Fix plugin can't get settings from lobe-chat.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **plugin**: Fix plugin can't get settings from lobe-chat, closes [#488](https://github.com/lobehub/lobe-chat/issues/488) ([1555140](https://github.com/lobehub/lobe-chat/commit/1555140))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.102.3](https://github.com/lobehub/lobe-chat/compare/v0.102.2...v0.102.3)

<sup>Released on **2023-11-20**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix plugin not work correct when adding agent from market.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix plugin not work correct when adding agent from market, closes [#394](https://github.com/lobehub/lobe-chat/issues/394) ([7c99816](https://github.com/lobehub/lobe-chat/commit/7c99816))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.102.2](https://github.com/lobehub/lobe-chat/compare/v0.102.1...v0.102.2)

<sup>Released on **2023-11-20**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix model tag missing.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix model tag missing, closes [#481](https://github.com/lobehub/lobe-chat/issues/481) ([8c96cf0](https://github.com/lobehub/lobe-chat/commit/8c96cf0))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.102.1](https://github.com/lobehub/lobe-chat/compare/v0.102.0...v0.102.1)

<sup>Released on **2023-11-19**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix image upload list missing.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix image upload list missing ([6bbac34](https://github.com/lobehub/lobe-chat/commit/6bbac34))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.102.0](https://github.com/lobehub/lobe-chat/compare/v0.101.7...v0.102.0)

<sup>Released on **2023-11-19**</sup>

#### ✨ Features

- **misc**: Support TTS & STT.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support TTS & STT, closes [#443](https://github.com/lobehub/lobe-chat/issues/443) ([4fa2ef4](https://github.com/lobehub/lobe-chat/commit/4fa2ef4))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.101.7](https://github.com/lobehub/lobe-chat/compare/v0.101.6...v0.101.7)

<sup>Released on **2023-11-18**</sup>

#### 🐛 Bug Fixes

- **misc**: Agent details sidebar and market page height overflow.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Agent details sidebar and market page height overflow ([71a54cc](https://github.com/lobehub/lobe-chat/commit/71a54cc))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.101.6](https://github.com/lobehub/lobe-chat/compare/v0.101.5...v0.101.6)

<sup>Released on **2023-11-17**</sup>

#### 💄 Styles

- **misc**: Add config to renderErrorMessages, Use new Alert ui.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add config to renderErrorMessages ([75b6b40](https://github.com/lobehub/lobe-chat/commit/75b6b40))
- **misc**: Use new Alert ui ([cf845a7](https://github.com/lobehub/lobe-chat/commit/cf845a7))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.101.5](https://github.com/lobehub/lobe-chat/compare/v0.101.4...v0.101.5)

<sup>Released on **2023-11-17**</sup>

#### 🐛 Bug Fixes

- **misc**: Improve openai error info.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Improve openai error info, closes [#469](https://github.com/lobehub/lobe-chat/issues/469) ([5523b64](https://github.com/lobehub/lobe-chat/commit/5523b64))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.101.4](https://github.com/lobehub/lobe-chat/compare/v0.101.3...v0.101.4)

<sup>Released on **2023-11-14**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix the plugin array merge error when fork agent from market.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix the plugin array merge error when fork agent from market, closes [#459](https://github.com/lobehub/lobe-chat/issues/459) ([fc29b33](https://github.com/lobehub/lobe-chat/commit/fc29b33))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.101.3](https://github.com/lobehub/lobe-chat/compare/v0.101.2...v0.101.3)

<sup>Released on **2023-11-14**</sup>

#### 💄 Styles

- **misc**: Improve password ui to make it more clear.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve password ui to make it more clear, closes [#458](https://github.com/lobehub/lobe-chat/issues/458) ([e3d2a8e](https://github.com/lobehub/lobe-chat/commit/e3d2a8e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.101.2](https://github.com/lobehub/lobe-chat/compare/v0.101.1...v0.101.2)

<sup>Released on **2023-11-14**</sup>

#### 💄 Styles

- **misc**: upload image to vision model adapting to mobile device.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: upload image to vision model adapting to mobile device, closes [#457](https://github.com/lobehub/lobe-chat/issues/457) ([9c4f4ee](https://github.com/lobehub/lobe-chat/commit/9c4f4ee))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.101.1](https://github.com/lobehub/lobe-chat/compare/v0.101.0...v0.101.1)

<sup>Released on **2023-11-14**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix market search (fix.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix market search (fix, closes [#437](https://github.com/lobehub/lobe-chat/issues/437) ([178b742](https://github.com/lobehub/lobe-chat/commit/178b742))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.101.0](https://github.com/lobehub/lobe-chat/compare/v0.100.5...v0.101.0)

<sup>Released on **2023-11-14**</sup>

#### ✨ Features

- **misc**: Support upload images to chat with gpt4-vision model.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support upload images to chat with gpt4-vision model, closes [#440](https://github.com/lobehub/lobe-chat/issues/440) ([858d047](https://github.com/lobehub/lobe-chat/commit/858d047))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.100.5](https://github.com/lobehub/lobe-chat/compare/v0.100.4...v0.100.5)

<sup>Released on **2023-11-11**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the input area to suit the files upload feature.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the input area to suit the files upload feature, closes [#442](https://github.com/lobehub/lobe-chat/issues/442) ([57a61fd](https://github.com/lobehub/lobe-chat/commit/57a61fd))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.100.4](https://github.com/lobehub/lobe-chat/compare/v0.100.3...v0.100.4)

<sup>Released on **2023-11-11**</sup>

#### 🐛 Bug Fixes

- **misc**: Hotkey disabled in form tags.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Hotkey disabled in form tags ([165888f](https://github.com/lobehub/lobe-chat/commit/165888f))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.100.3](https://github.com/lobehub/lobe-chat/compare/v0.100.2...v0.100.3)

<sup>Released on **2023-11-09**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix market error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix market error ([3d7550c](https://github.com/lobehub/lobe-chat/commit/3d7550c))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.100.2](https://github.com/lobehub/lobe-chat/compare/v0.100.1...v0.100.2)

<sup>Released on **2023-11-09**</sup>

#### 🐛 Bug Fixes

- **misc**: Upgrade viewport for nextjs 14.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Upgrade viewport for nextjs 14, closes [#436](https://github.com/lobehub/lobe-chat/issues/436) ([57d3d07](https://github.com/lobehub/lobe-chat/commit/57d3d07))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.100.1](https://github.com/lobehub/lobe-chat/compare/v0.100.0...v0.100.1)

<sup>Released on **2023-11-09**</sup>

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>
</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.100.0](https://github.com/lobehub/lobe-chat/compare/v0.99.1...v0.100.0)

<sup>Released on **2023-11-09**</sup>

#### ✨ Features

- **hotkeys**: Compatible with SSR, display platform specific key.
- **misc**: Platform check utils.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **hotkeys**: Compatible with SSR ([99fa4f8](https://github.com/lobehub/lobe-chat/commit/99fa4f8))
- **hotkeys**: Display platform specific key ([ee332a4](https://github.com/lobehub/lobe-chat/commit/ee332a4))
- **misc**: Platform check utils ([08a3cb9](https://github.com/lobehub/lobe-chat/commit/08a3cb9))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.99.1](https://github.com/lobehub/lobe-chat/compare/v0.99.0...v0.99.1)

<sup>Released on **2023-11-08**</sup>

#### 💄 Styles

- **misc**: Add max height to model menu in chat input area.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add max height to model menu in chat input area, closes [#430](https://github.com/lobehub/lobe-chat/issues/430) ([c9a86f3](https://github.com/lobehub/lobe-chat/commit/c9a86f3))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.99.0](https://github.com/lobehub/lobe-chat/compare/v0.98.3...v0.99.0)

<sup>Released on **2023-11-08**</sup>

#### ✨ Features

- **misc**: Add Environment Variable for custom model name when deploying.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add Environment Variable for custom model name when deploying, closes [#429](https://github.com/lobehub/lobe-chat/issues/429) ([15f9fa2](https://github.com/lobehub/lobe-chat/commit/15f9fa2))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.98.3](https://github.com/lobehub/lobe-chat/compare/v0.98.2...v0.98.3)

<sup>Released on **2023-11-07**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix redirect to welcome problem when there are topics in inbox.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix redirect to welcome problem when there are topics in inbox, closes [#422](https://github.com/lobehub/lobe-chat/issues/422) ([3d2588a](https://github.com/lobehub/lobe-chat/commit/3d2588a))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.98.2](https://github.com/lobehub/lobe-chat/compare/v0.98.1...v0.98.2)

<sup>Released on **2023-11-07**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor antd locale file to useSWR.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor antd locale file to useSWR ([2e1cd7c](https://github.com/lobehub/lobe-chat/commit/2e1cd7c))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.98.1](https://github.com/lobehub/lobe-chat/compare/v0.98.0...v0.98.1)

<sup>Released on **2023-11-07**</sup>

#### 💄 Styles

- **misc**: Update welcome assetes.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update welcome assetes ([8840554](https://github.com/lobehub/lobe-chat/commit/8840554))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.98.0](https://github.com/lobehub/lobe-chat/compare/v0.97.1...v0.98.0)

<sup>Released on **2023-11-07**</sup>

#### ✨ Features

- **misc**: Support latest openai model.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support latest openai model, closes [#417](https://github.com/lobehub/lobe-chat/issues/417) ([46386dc](https://github.com/lobehub/lobe-chat/commit/46386dc))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.97.1](https://github.com/lobehub/lobe-chat/compare/v0.97.0...v0.97.1)

<sup>Released on **2023-11-06**</sup>

#### 🐛 Bug Fixes

- **misc**: Use pnpm to fix docker release.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Use pnpm to fix docker release ([886cc3b](https://github.com/lobehub/lobe-chat/commit/886cc3b))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.97.0](https://github.com/lobehub/lobe-chat/compare/v0.96.9...v0.97.0)

<sup>Released on **2023-11-05**</sup>

#### ✨ Features

- **misc**: Add open new topic when open a topic.

#### 🐛 Bug Fixes

- **misc**: Fix toggle back to default topic when clearing topic.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add open new topic when open a topic ([4df6384](https://github.com/lobehub/lobe-chat/commit/4df6384))

#### What's fixed

- **misc**: Fix toggle back to default topic when clearing topic ([6fe0a5c](https://github.com/lobehub/lobe-chat/commit/6fe0a5c))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.96.9](https://github.com/lobehub/lobe-chat/compare/v0.96.8...v0.96.9)

<sup>Released on **2023-11-04**</sup>

#### 💄 Styles

- **misc**: Update topic list header.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update topic list header ([ce932d7](https://github.com/lobehub/lobe-chat/commit/ce932d7))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.96.8](https://github.com/lobehub/lobe-chat/compare/v0.96.7...v0.96.8)

<sup>Released on **2023-10-31**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix a bug that weather plugin is not work correctly, template remove sharp deps.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix a bug that weather plugin is not work correctly ([dbb65ff](https://github.com/lobehub/lobe-chat/commit/dbb65ff))
- **misc**: Template remove sharp deps ([380723d](https://github.com/lobehub/lobe-chat/commit/380723d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.96.7](https://github.com/lobehub/lobe-chat/compare/v0.96.6...v0.96.7)

<sup>Released on **2023-10-31**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix a bug when click inbox not switch back to chat page.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix a bug when click inbox not switch back to chat page ([31f6d29](https://github.com/lobehub/lobe-chat/commit/31f6d29))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.96.6](https://github.com/lobehub/lobe-chat/compare/v0.96.5...v0.96.6)

<sup>Released on **2023-10-30**</sup>

#### 🐛 Bug Fixes

- **misc**: Improve plausible analytics ENV.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Improve plausible analytics ENV ([aa851d4](https://github.com/lobehub/lobe-chat/commit/aa851d4))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.96.5](https://github.com/lobehub/lobe-chat/compare/v0.96.4...v0.96.5)

<sup>Released on **2023-10-29**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix docker image optimization error log.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix docker image optimization error log ([730aec1](https://github.com/lobehub/lobe-chat/commit/730aec1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.96.4](https://github.com/lobehub/lobe-chat/compare/v0.96.3...v0.96.4)

<sup>Released on **2023-10-29**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix agents market locale fallback to english.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix agents market locale fallback to english, closes [#382](https://github.com/lobehub/lobe-chat/issues/382) ([3814523](https://github.com/lobehub/lobe-chat/commit/3814523))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.96.3](https://github.com/lobehub/lobe-chat/compare/v0.96.2...v0.96.3)

<sup>Released on **2023-10-28**</sup>

#### 💄 Styles

- **misc**: Fix SessionList on mobile.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix SessionList on mobile ([e7e7b80](https://github.com/lobehub/lobe-chat/commit/e7e7b80))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.96.2](https://github.com/lobehub/lobe-chat/compare/v0.96.1...v0.96.2)

<sup>Released on **2023-10-28**</sup>

#### 💄 Styles

- **misc**: Fix some styles and make updates to various files.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix some styles and make updates to various files ([44a5f0a](https://github.com/lobehub/lobe-chat/commit/44a5f0a))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.96.1](https://github.com/lobehub/lobe-chat/compare/v0.96.0...v0.96.1)

<sup>Released on **2023-10-28**</sup>

#### 💄 Styles

- **misc**: Add guide to market page.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add guide to market page ([8a794f9](https://github.com/lobehub/lobe-chat/commit/8a794f9))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.96.0](https://github.com/lobehub/lobe-chat/compare/v0.95.1...v0.96.0)

<sup>Released on **2023-10-27**</sup>

#### ✨ Features

- **misc**: Improve pin mode about session group.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Improve pin mode about session group, closes [#369](https://github.com/lobehub/lobe-chat/issues/369) ([75c5883](https://github.com/lobehub/lobe-chat/commit/75c5883))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.95.1](https://github.com/lobehub/lobe-chat/compare/v0.95.0...v0.95.1)

<sup>Released on **2023-10-25**</sup>

#### 💄 Styles

- **misc**: Improve plugin message ui.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve plugin message ui ([6edd25b](https://github.com/lobehub/lobe-chat/commit/6edd25b))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.95.0](https://github.com/lobehub/lobe-chat/compare/v0.94.5...v0.95.0)

<sup>Released on **2023-10-24**</sup>

#### ♻ Code Refactoring

- **misc**: 优化 plugin 文件夹命名以支持 standalone 类型的插件.

#### ✨ Features

- **misc**: Support function call at message end, support plugin settings modal, support plugin state and settings.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 优化 plugin 文件夹命名以支持 standalone 类型的插件 ([98860a8](https://github.com/lobehub/lobe-chat/commit/98860a8))

#### What's improved

- **misc**: Support function call at message end, closes [#357](https://github.com/lobehub/lobe-chat/issues/357) ([e195fdb](https://github.com/lobehub/lobe-chat/commit/e195fdb))
- **misc**: Support plugin settings modal ([f47b6fa](https://github.com/lobehub/lobe-chat/commit/f47b6fa))
- **misc**: Support plugin state and settings ([10829a4](https://github.com/lobehub/lobe-chat/commit/10829a4))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.94.5](https://github.com/lobehub/lobe-chat/compare/v0.94.4...v0.94.5)

<sup>Released on **2023-10-22**</sup>

#### 🐛 Bug Fixes

- **misc**: Fallback agent market index to en when not find correct locale.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fallback agent market index to en when not find correct locale, closes [#355](https://github.com/lobehub/lobe-chat/issues/355) ([7a45ab4](https://github.com/lobehub/lobe-chat/commit/7a45ab4))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.94.4](https://github.com/lobehub/lobe-chat/compare/v0.94.3...v0.94.4)

<sup>Released on **2023-10-21**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix break cn chars in stream mode.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix break cn chars in stream mode, closes [#347](https://github.com/lobehub/lobe-chat/issues/347) ([f831447](https://github.com/lobehub/lobe-chat/commit/f831447))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.94.3](https://github.com/lobehub/lobe-chat/compare/v0.94.2...v0.94.3)

<sup>Released on **2023-10-19**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix agent share format.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix agent share format ([56ebc0b](https://github.com/lobehub/lobe-chat/commit/56ebc0b))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.94.2](https://github.com/lobehub/lobe-chat/compare/v0.94.1...v0.94.2)

<sup>Released on **2023-10-19**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix agent market with other locales.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix agent market with other locales ([2414d34](https://github.com/lobehub/lobe-chat/commit/2414d34))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.94.1](https://github.com/lobehub/lobe-chat/compare/v0.94.0...v0.94.1)

<sup>Released on **2023-10-19**</sup>

#### 💄 Styles

- **misc**: Update ShareAgentButton.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update ShareAgentButton ([c396bd7](https://github.com/lobehub/lobe-chat/commit/c396bd7))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.94.0](https://github.com/lobehub/lobe-chat/compare/v0.93.0...v0.94.0)

<sup>Released on **2023-10-18**</sup>

#### ✨ Features

- **misc**: Add agent share.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add agent share ([953d7c7](https://github.com/lobehub/lobe-chat/commit/953d7c7))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.93.0](https://github.com/lobehub/lobe-chat/compare/v0.92.0...v0.93.0)

<sup>Released on **2023-10-18**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor chain.

#### ✨ Features

- **misc**: Support multi-language translate.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor chain ([49c4863](https://github.com/lobehub/lobe-chat/commit/49c4863))

#### What's improved

- **misc**: Support multi-language translate ([548bc5d](https://github.com/lobehub/lobe-chat/commit/548bc5d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.92.0](https://github.com/lobehub/lobe-chat/compare/v0.91.0...v0.92.0)

<sup>Released on **2023-10-18**</sup>

#### ✨ Features

- **misc**: Support translate message to current language.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support translate message to current language, closes [#340](https://github.com/lobehub/lobe-chat/issues/340) ([cf15f1e](https://github.com/lobehub/lobe-chat/commit/cf15f1e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.91.0](https://github.com/lobehub/lobe-chat/compare/v0.90.3...v0.91.0)

<sup>Released on **2023-10-17**</sup>

#### ✨ Features

- **misc**: Add hotkeys.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add hotkeys, closes [#286](https://github.com/lobehub/lobe-chat/issues/286) ([041054d](https://github.com/lobehub/lobe-chat/commit/041054d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.90.3](https://github.com/lobehub/lobe-chat/compare/v0.90.2...v0.90.3)

<sup>Released on **2023-10-17**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix ActionBar props and regenerate btn with error message.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix ActionBar props and regenerate btn with error message, closes [#337](https://github.com/lobehub/lobe-chat/issues/337) ([246e8fd](https://github.com/lobehub/lobe-chat/commit/246e8fd))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.90.2](https://github.com/lobehub/lobe-chat/compare/v0.90.1...v0.90.2)

<sup>Released on **2023-10-17**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor OpenAIStreamPayload with chat name.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor OpenAIStreamPayload with chat name ([a799530](https://github.com/lobehub/lobe-chat/commit/a799530))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.90.1](https://github.com/lobehub/lobe-chat/compare/v0.90.0...v0.90.1)

<sup>Released on **2023-10-17**</sup>

#### 💄 Styles

- **misc**: Fix lazyload height.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix lazyload height ([98efe02](https://github.com/lobehub/lobe-chat/commit/98efe02))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.90.0](https://github.com/lobehub/lobe-chat/compare/v0.89.10...v0.90.0)

<sup>Released on **2023-10-17**</sup>

#### ✨ Features

- **misc**: Add Lazyload.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add Lazyload ([27d6cb7](https://github.com/lobehub/lobe-chat/commit/27d6cb7))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.89.10](https://github.com/lobehub/lobe-chat/compare/v0.89.9...v0.89.10)

<sup>Released on **2023-10-17**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor ChatList onActionsClick.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor ChatList onActionsClick ([d06d87e](https://github.com/lobehub/lobe-chat/commit/d06d87e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.89.9](https://github.com/lobehub/lobe-chat/compare/v0.89.8...v0.89.9)

<sup>Released on **2023-10-17**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix ChatList FC Render.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix ChatList FC Render ([4b8bdbd](https://github.com/lobehub/lobe-chat/commit/4b8bdbd))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.89.8](https://github.com/lobehub/lobe-chat/compare/v0.89.7...v0.89.8)

<sup>Released on **2023-10-16**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor ChatList.

#### 🐛 Bug Fixes

- **misc**: Fix type.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor ChatList, closes [#147](https://github.com/lobehub/lobe-chat/issues/147) ([aa4216c](https://github.com/lobehub/lobe-chat/commit/aa4216c))

#### What's fixed

- **misc**: Fix type ([1e931d5](https://github.com/lobehub/lobe-chat/commit/1e931d5))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.89.7](https://github.com/lobehub/lobe-chat/compare/v0.89.6...v0.89.7)

<sup>Released on **2023-10-16**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix setting tab highlight (fix.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix setting tab highlight (fix, closes [#332](https://github.com/lobehub/lobe-chat/issues/332) ([d288f9d](https://github.com/lobehub/lobe-chat/commit/d288f9d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.89.6](https://github.com/lobehub/lobe-chat/compare/v0.89.5...v0.89.6)

<sup>Released on **2023-10-15**</sup>

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>
</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.89.5](https://github.com/lobehub/lobe-chat/compare/v0.89.4...v0.89.5)

<sup>Released on **2023-10-15**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix fallback to en when the locale is zh, fix reset button not clear plugin settings.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix fallback to en when the locale is zh ([ff2c00e](https://github.com/lobehub/lobe-chat/commit/ff2c00e))
- **misc**: Fix reset button not clear plugin settings ([aa1e657](https://github.com/lobehub/lobe-chat/commit/aa1e657))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.89.4](https://github.com/lobehub/lobe-chat/compare/v0.89.3...v0.89.4)

<sup>Released on **2023-10-15**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix qwen, chatglm request failed.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix qwen, chatglm request failed, closes [#318](https://github.com/lobehub/lobe-chat/issues/318) ([a5699e2](https://github.com/lobehub/lobe-chat/commit/a5699e2))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.89.3](https://github.com/lobehub/lobe-chat/compare/v0.89.2...v0.89.3)

<sup>Released on **2023-10-12**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix plugin error with nginx reverse proxy.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix plugin error with nginx reverse proxy, closes [#315](https://github.com/lobehub/lobe-chat/issues/315) ([3ba3a3b](https://github.com/lobehub/lobe-chat/commit/3ba3a3b))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.89.2](https://github.com/lobehub/lobe-chat/compare/v0.89.1...v0.89.2)

<sup>Released on **2023-10-12**</sup>

#### 💄 Styles

- **misc**: Modify onClick event in SessionHeader, change title in Loading component,.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Modify onClick event in SessionHeader, change title in Loading component, ([b984f6a](https://github.com/lobehub/lobe-chat/commit/b984f6a))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.89.1](https://github.com/lobehub/lobe-chat/compare/v0.89.0...v0.89.1)

<sup>Released on **2023-10-12**</sup>

#### 🐛 Bug Fixes

- **misc**: Remove useless dynamic import.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Remove useless dynamic import ([4a9c426](https://github.com/lobehub/lobe-chat/commit/4a9c426))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.89.0](https://github.com/lobehub/lobe-chat/compare/v0.88.0...v0.89.0)

<sup>Released on **2023-10-12**</sup>

#### ✨ Features

- **agent-card**: Add and modify features for agent card.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **agent-card**: Add and modify features for agent card ([3e3090a](https://github.com/lobehub/lobe-chat/commit/3e3090a))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.88.0](https://github.com/lobehub/lobe-chat/compare/v0.87.0...v0.88.0)

<sup>Released on **2023-10-11**</sup>

#### ✨ Features

- **misc**: Add mobile responsiveness, create new component, modify properties, make API calls, Dynamically import components using "dynamic" function.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add mobile responsiveness, create new component, modify properties, make API calls ([759c920](https://github.com/lobehub/lobe-chat/commit/759c920))
- **misc**: Dynamically import components using "dynamic" function ([dd9db22](https://github.com/lobehub/lobe-chat/commit/dd9db22))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.87.0](https://github.com/lobehub/lobe-chat/compare/v0.86.5...v0.87.0)

<sup>Released on **2023-10-11**</sup>

#### ✨ Features

- **misc**: Support custom model name.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support custom model name, closes [#305](https://github.com/lobehub/lobe-chat/issues/305) ([84a066a](https://github.com/lobehub/lobe-chat/commit/84a066a))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.86.5](https://github.com/lobehub/lobe-chat/compare/v0.86.4...v0.86.5)

<sup>Released on **2023-10-11**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix clear session error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix clear session error, closes [#303](https://github.com/lobehub/lobe-chat/issues/303) ([09512fc](https://github.com/lobehub/lobe-chat/commit/09512fc))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.86.4](https://github.com/lobehub/lobe-chat/compare/v0.86.3...v0.86.4)

<sup>Released on **2023-10-11**</sup>

#### 💄 Styles

- **misc**: Improve api key form.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve api key form ([fa3170d](https://github.com/lobehub/lobe-chat/commit/fa3170d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.86.3](https://github.com/lobehub/lobe-chat/compare/v0.86.2...v0.86.3)

<sup>Released on **2023-10-11**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix docker image.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix docker image ([14ff80e](https://github.com/lobehub/lobe-chat/commit/14ff80e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.86.2](https://github.com/lobehub/lobe-chat/compare/v0.86.1...v0.86.2)

<sup>Released on **2023-10-11**</sup>

#### 🐛 Bug Fixes

- **docker**: Improve config to reduce unnecessary env and change default PORT.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **docker**: Improve config to reduce unnecessary env and change default PORT, closes [#298](https://github.com/lobehub/lobe-chat/issues/298) ([6061318](https://github.com/lobehub/lobe-chat/commit/6061318))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.86.1](https://github.com/lobehub/lobe-chat/compare/v0.86.0...v0.86.1)

<sup>Released on **2023-10-11**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix docker reverse proxy don't work.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix docker reverse proxy don't work, closes [#294](https://github.com/lobehub/lobe-chat/issues/294) ([a51ba1d](https://github.com/lobehub/lobe-chat/commit/a51ba1d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.86.0](https://github.com/lobehub/lobe-chat/compare/v0.85.3...v0.86.0)

<sup>Released on **2023-10-10**</sup>

#### ✨ Features

- **misc**: Support docker deploy.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support docker deploy, closes [#283](https://github.com/lobehub/lobe-chat/issues/283) ([5bbc87c](https://github.com/lobehub/lobe-chat/commit/5bbc87c))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.85.3](https://github.com/lobehub/lobe-chat/compare/v0.85.2...v0.85.3)

<sup>Released on **2023-10-10**</sup>

#### 💄 Styles

- **misc**: Add new components, modify display properties, and update settings feature, Replace 100vh with 100% to fix mobile scroll problem.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Add new components, modify display properties, and update settings feature ([87a4a46](https://github.com/lobehub/lobe-chat/commit/87a4a46))
- **misc**: Replace 100vh with 100% to fix mobile scroll problem ([2ef3c94](https://github.com/lobehub/lobe-chat/commit/2ef3c94))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.85.2](https://github.com/lobehub/lobe-chat/compare/v0.85.1...v0.85.2)

<sup>Released on **2023-10-10**</sup>

#### 🐛 Bug Fixes

- **misc**: Add apikey form when there is no default api key in env.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Add apikey form when there is no default api key in env, closes [#290](https://github.com/lobehub/lobe-chat/issues/290) ([2c907e9](https://github.com/lobehub/lobe-chat/commit/2c907e9))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.85.1](https://github.com/lobehub/lobe-chat/compare/v0.85.0...v0.85.1)

<sup>Released on **2023-10-10**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix mobile safe area (fix.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix mobile safe area (fix, closes [#211](https://github.com/lobehub/lobe-chat/issues/211) ([68775b8](https://github.com/lobehub/lobe-chat/commit/68775b8))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.85.0](https://github.com/lobehub/lobe-chat/compare/v0.84.0...v0.85.0)

<sup>Released on **2023-10-10**</sup>

#### ✨ Features

- **misc**: Add ja_JP, ko_KR and update workflow.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add ja_JP, ko_KR and update workflow ([57512a0](https://github.com/lobehub/lobe-chat/commit/57512a0))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.84.0](https://github.com/lobehub/lobe-chat/compare/v0.83.10...v0.84.0)

<sup>Released on **2023-10-10**</sup>

#### ✨ Features

- **misc**: Support detect new version and upgrade action.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support detect new version and upgrade action, closes [#282](https://github.com/lobehub/lobe-chat/issues/282) ([5da19b2](https://github.com/lobehub/lobe-chat/commit/5da19b2))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.83.10](https://github.com/lobehub/lobe-chat/compare/v0.83.9...v0.83.10)

<sup>Released on **2023-10-09**</sup>

#### ♻ Code Refactoring

- **layout**: Refactor layout, Refactor settings layout, Refactor ssc layout.
- **share**: Use modern-screenshot.

#### 🐛 Bug Fixes

- **misc**: Fix rsc layout.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **layout**: Refactor layout ([ace21f4](https://github.com/lobehub/lobe-chat/commit/ace21f4))
- **layout**: Refactor settings layout ([bd48121](https://github.com/lobehub/lobe-chat/commit/bd48121))
- **layout**: Refactor ssc layout ([26e1c41](https://github.com/lobehub/lobe-chat/commit/26e1c41))
- **share**: Use modern-screenshot, closes [#256](https://github.com/lobehub/lobe-chat/issues/256) ([b3d7108](https://github.com/lobehub/lobe-chat/commit/b3d7108))

#### What's fixed

- **misc**: Fix rsc layout ([d73f13f](https://github.com/lobehub/lobe-chat/commit/d73f13f))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.83.9](https://github.com/lobehub/lobe-chat/compare/v0.83.8...v0.83.9)

<sup>Released on **2023-10-08**</sup>

#### ♻ Code Refactoring

- **agent-market**: Refactor desktop and mobile to improve mobile performance.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **agent-market**: Refactor desktop and mobile to improve mobile performance, closes [#278](https://github.com/lobehub/lobe-chat/issues/278) ([82b7f60](https://github.com/lobehub/lobe-chat/commit/82b7f60))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.83.8](https://github.com/lobehub/lobe-chat/compare/v0.83.7...v0.83.8)

<sup>Released on **2023-10-07**</sup>

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>
</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.83.7](https://github.com/lobehub/lobe-chat/compare/v0.83.6...v0.83.7)

<sup>Released on **2023-10-07**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix shuffle, use search url with agent item.

#### 💄 Styles

- **misc**: Better tag style, improve loading state.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix shuffle ([d4b9dc3](https://github.com/lobehub/lobe-chat/commit/d4b9dc3))
- **misc**: Use search url with agent item ([98df623](https://github.com/lobehub/lobe-chat/commit/98df623))

#### Styles

- **misc**: Better tag style ([38e42ea](https://github.com/lobehub/lobe-chat/commit/38e42ea))
- **misc**: Improve loading state ([f00c868](https://github.com/lobehub/lobe-chat/commit/f00c868))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.83.6](https://github.com/lobehub/lobe-chat/compare/v0.83.5...v0.83.6)

<sup>Released on **2023-10-06**</sup>

#### 💄 Styles

- **misc**: Update modal style.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update modal style ([2ab1475](https://github.com/lobehub/lobe-chat/commit/2ab1475))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.83.5](https://github.com/lobehub/lobe-chat/compare/v0.83.4...v0.83.5)

<sup>Released on **2023-10-06**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix agent market list.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix agent market list, closes [#273](https://github.com/lobehub/lobe-chat/issues/273) ([c020277](https://github.com/lobehub/lobe-chat/commit/c020277))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.83.4](https://github.com/lobehub/lobe-chat/compare/v0.83.3...v0.83.4)

<sup>Released on **2023-10-06**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix agent settings.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix agent settings, closes [#271](https://github.com/lobehub/lobe-chat/issues/271) ([aac9a70](https://github.com/lobehub/lobe-chat/commit/aac9a70))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.83.3](https://github.com/lobehub/lobe-chat/compare/v0.83.2...v0.83.3)

<sup>Released on **2023-10-06**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the settings layout to rsc.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the settings layout to rsc ([b840f44](https://github.com/lobehub/lobe-chat/commit/b840f44))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.83.2](https://github.com/lobehub/lobe-chat/compare/v0.83.1...v0.83.2)

<sup>Released on **2023-10-06**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix setCookie method that set cookie with sub-path.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix setCookie method that set cookie with sub-path, closes [#269](https://github.com/lobehub/lobe-chat/issues/269) ([1b859b7](https://github.com/lobehub/lobe-chat/commit/1b859b7))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.83.1](https://github.com/lobehub/lobe-chat/compare/v0.83.0...v0.83.1)

<sup>Released on **2023-10-06**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor settings page entry.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor settings page entry ([e86aff2](https://github.com/lobehub/lobe-chat/commit/e86aff2))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.83.0](https://github.com/lobehub/lobe-chat/compare/v0.82.9...v0.83.0)

<sup>Released on **2023-10-06**</sup>

#### ✨ Features

- **misc**: Upgrade locale with SSR.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Upgrade locale with SSR, closes [#268](https://github.com/lobehub/lobe-chat/issues/268) ([2fdea52](https://github.com/lobehub/lobe-chat/commit/2fdea52))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.82.9](https://github.com/lobehub/lobe-chat/compare/v0.82.8...v0.82.9)

<sup>Released on **2023-10-05**</sup>

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>
</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.82.8](https://github.com/lobehub/lobe-chat/compare/v0.82.7...v0.82.8)

<sup>Released on **2023-09-30**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor / route to reduce page js size.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor / route to reduce page js size ([79f0347](https://github.com/lobehub/lobe-chat/commit/79f0347))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.82.7](https://github.com/lobehub/lobe-chat/compare/v0.82.6...v0.82.7)

<sup>Released on **2023-09-30**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the api router to app route handlers.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the api router to app route handlers, closes [#254](https://github.com/lobehub/lobe-chat/issues/254) ([f032112](https://github.com/lobehub/lobe-chat/commit/f032112))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.82.6](https://github.com/lobehub/lobe-chat/compare/v0.82.5...v0.82.6)

<sup>Released on **2023-09-29**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix share default config, pin openai to fix type error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix share default config ([e00d6bf](https://github.com/lobehub/lobe-chat/commit/e00d6bf))
- **misc**: Pin openai to fix type error ([5af4050](https://github.com/lobehub/lobe-chat/commit/5af4050))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.82.5](https://github.com/lobehub/lobe-chat/compare/v0.82.4...v0.82.5)

<sup>Released on **2023-09-29**</sup>

#### 💄 Styles

- **misc**: Update theme color and styling of mobile settings page.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update theme color and styling of mobile settings page ([1acfb71](https://github.com/lobehub/lobe-chat/commit/1acfb71))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.82.4](https://github.com/lobehub/lobe-chat/compare/v0.82.3...v0.82.4)

<sup>Released on **2023-09-29**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正 localStorage 不存在造成设置页刷新 500 保存的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正 localStorage 不存在造成设置页刷新 500 保存的问题 ([b894cc8](https://github.com/lobehub/lobe-chat/commit/b894cc8))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.82.3](https://github.com/lobehub/lobe-chat/compare/v0.82.2...v0.82.3)

<sup>Released on **2023-09-29**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正 access code 校验逻辑，修正 api key 无法正常显示在秘钥输入框，并增加显示关闭按钮，修正移动端输入 access code 默认打开数据键盘的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正 access code 校验逻辑，closes [#184](https://github.com/lobehub/lobe-chat/issues/184) ([a7301c3](https://github.com/lobehub/lobe-chat/commit/a7301c3))
- **misc**: 修正 api key 无法正常显示在秘钥输入框，并增加显示关闭按钮，closes [#182](https://github.com/lobehub/lobe-chat/issues/182) ([def1153](https://github.com/lobehub/lobe-chat/commit/def1153))
- **misc**: 修正移动端输入 access code 默认打开数据键盘的问题 ([7994982](https://github.com/lobehub/lobe-chat/commit/7994982))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.82.2](https://github.com/lobehub/lobe-chat/compare/v0.82.1...v0.82.2)

<sup>Released on **2023-09-28**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor settings page and mobile ux.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor settings page and mobile ux ([89c5648](https://github.com/lobehub/lobe-chat/commit/89c5648))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.82.1](https://github.com/lobehub/lobe-chat/compare/v0.82.0...v0.82.1)

<sup>Released on **2023-09-27**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix share screenshot scrollbar.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix share screenshot scrollbar ([244b3b4](https://github.com/lobehub/lobe-chat/commit/244b3b4))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.82.0](https://github.com/lobehub/lobe-chat/compare/v0.81.0...v0.82.0)

<sup>Released on **2023-09-27**</sup>

#### ✨ Features

- **share**: Add screenshot.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **share**: Add screenshot, closes [#152](https://github.com/lobehub/lobe-chat/issues/152) ([f5d21f4](https://github.com/lobehub/lobe-chat/commit/f5d21f4))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.81.0](https://github.com/lobehub/lobe-chat/compare/v0.80.2...v0.81.0)

<sup>Released on **2023-09-27**</sup>

#### ✨ Features

- **misc**: Add several analytics sdk.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add several analytics sdk, closes [#244](https://github.com/lobehub/lobe-chat/issues/244) ([65c6c93](https://github.com/lobehub/lobe-chat/commit/65c6c93))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.80.2](https://github.com/lobehub/lobe-chat/compare/v0.80.1...v0.80.2)

<sup>Released on **2023-09-27**</sup>

#### 💄 Styles

- **misc**: Switch Modal components to @lobehub/ui.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Switch Modal components to @lobehub/ui ([d056015](https://github.com/lobehub/lobe-chat/commit/d056015))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.80.1](https://github.com/lobehub/lobe-chat/compare/v0.80.0...v0.80.1)

<sup>Released on **2023-09-27**</sup>

#### 💄 Styles

- **misc**: Fix conversation mobile view area.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix conversation mobile view area ([6668e11](https://github.com/lobehub/lobe-chat/commit/6668e11))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.80.0](https://github.com/lobehub/lobe-chat/compare/v0.79.8...v0.80.0)

<sup>Released on **2023-09-27**</sup>

#### ✨ Features

- **misc**: Improve user experience and ensure consistency.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Improve user experience and ensure consistency ([abba584](https://github.com/lobehub/lobe-chat/commit/abba584))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.79.8](https://github.com/lobehub/lobe-chat/compare/v0.79.7...v0.79.8)

<sup>Released on **2023-09-27**</sup>

#### 💄 Styles

- **misc**: Fix safearea in mobile.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix safearea in mobile ([2adfb04](https://github.com/lobehub/lobe-chat/commit/2adfb04))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.79.7](https://github.com/lobehub/lobe-chat/compare/v0.79.6...v0.79.7)

<sup>Released on **2023-09-27**</sup>

#### ♻ Code Refactoring

- **misc**: Use hook to check PWA env.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Use hook to check PWA env ([b4234db](https://github.com/lobehub/lobe-chat/commit/b4234db))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.79.6](https://github.com/lobehub/lobe-chat/compare/v0.79.5...v0.79.6)

<sup>Released on **2023-09-27**</sup>

#### 💄 Styles

- **misc**: Optimize PWA style and scroll effect.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Optimize PWA style and scroll effect ([0ae05b8](https://github.com/lobehub/lobe-chat/commit/0ae05b8))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.79.5](https://github.com/lobehub/lobe-chat/compare/v0.79.4...v0.79.5)

<sup>Released on **2023-09-26**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix URI error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix URI error ([282a0c8](https://github.com/lobehub/lobe-chat/commit/282a0c8))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.79.4](https://github.com/lobehub/lobe-chat/compare/v0.79.3...v0.79.4)

<sup>Released on **2023-09-26**</sup>

#### ♻ Code Refactoring

- **misc**: Move dir from page to app and remove .page suffix.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Move dir from page to app and remove .page suffix, closes [#236](https://github.com/lobehub/lobe-chat/issues/236) ([2907303](https://github.com/lobehub/lobe-chat/commit/2907303))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.79.3](https://github.com/lobehub/lobe-chat/compare/v0.79.2...v0.79.3)

<sup>Released on **2023-09-25**</sup>

#### 💄 Styles

- **meta**: Update meta image.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **meta**: Update meta image, closes [#66](https://github.com/lobehub/lobe-chat/issues/66) ([a71ffff](https://github.com/lobehub/lobe-chat/commit/a71ffff))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.79.2](https://github.com/lobehub/lobe-chat/compare/v0.79.1...v0.79.2)

<sup>Released on **2023-09-25**</sup>

#### 💄 Styles

- **meta**: Fix and add metadata.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **meta**: Fix and add metadata ([c872522](https://github.com/lobehub/lobe-chat/commit/c872522))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.79.1](https://github.com/lobehub/lobe-chat/compare/v0.79.0...v0.79.1)

<sup>Released on **2023-09-25**</sup>

#### ♻ Code Refactoring

- **migration**: Next.js app router.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **migration**: Next.js app router, closes [#220](https://github.com/lobehub/lobe-chat/issues/220) ([bb8085e](https://github.com/lobehub/lobe-chat/commit/bb8085e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.79.0](https://github.com/lobehub/lobe-chat/compare/v0.78.1...v0.79.0)

<sup>Released on **2023-09-25**</sup>

#### ✨ Features

- **conversation**: Add history range divider.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **conversation**: Add history range divider, closes [#118](https://github.com/lobehub/lobe-chat/issues/118) ([92d2c96](https://github.com/lobehub/lobe-chat/commit/92d2c96))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.78.1](https://github.com/lobehub/lobe-chat/compare/v0.78.0...v0.78.1)

<sup>Released on **2023-09-21**</sup>

#### 💄 Styles

- **misc**: Show topic tooltip on left side.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Show topic tooltip on left side ([f686fd2](https://github.com/lobehub/lobe-chat/commit/f686fd2))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.78.0](https://github.com/lobehub/lobe-chat/compare/v0.77.2...v0.78.0)

<sup>Released on **2023-09-17**</sup>

#### ✨ Features

- **misc**: Auto create topic when chatting.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Auto create topic when chatting, closes [#203](https://github.com/lobehub/lobe-chat/issues/203) ([f952792](https://github.com/lobehub/lobe-chat/commit/f952792))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.77.2](https://github.com/lobehub/lobe-chat/compare/v0.77.1...v0.77.2)

<sup>Released on **2023-09-15**</sup>

#### 🐛 Bug Fixes

- **settings**: Fix settings route.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **settings**: Fix settings route, closes [#195](https://github.com/lobehub/lobe-chat/issues/195) ([1b7d84e](https://github.com/lobehub/lobe-chat/commit/1b7d84e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.77.1](https://github.com/lobehub/lobe-chat/compare/v0.77.0...v0.77.1)

<sup>Released on **2023-09-14**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix lint.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix lint ([9f4f9d7](https://github.com/lobehub/lobe-chat/commit/9f4f9d7))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.77.0](https://github.com/lobehub/lobe-chat/compare/v0.76.2...v0.77.0)

<sup>Released on **2023-09-14**</sup>

#### ✨ Features

- **misc**: Update localization files and add translations for different languages.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Update localization files and add translations for different languages ([0157f92](https://github.com/lobehub/lobe-chat/commit/0157f92))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.76.2](https://github.com/lobehub/lobe-chat/compare/v0.76.1...v0.76.2)

<sup>Released on **2023-09-11**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix client config.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix client config ([d62f1b3](https://github.com/lobehub/lobe-chat/commit/d62f1b3))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.76.1](https://github.com/lobehub/lobe-chat/compare/v0.76.0...v0.76.1)

<sup>Released on **2023-09-11**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix save topic button.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix save topic button ([871905f](https://github.com/lobehub/lobe-chat/commit/871905f))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.76.0](https://github.com/lobehub/lobe-chat/compare/v0.75.0...v0.76.0)

<sup>Released on **2023-09-11**</sup>

#### ✨ Features

- **misc**: Support Azure OpenAI Deploy env.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support Azure OpenAI Deploy env, closes [#183](https://github.com/lobehub/lobe-chat/issues/183) ([bda6732](https://github.com/lobehub/lobe-chat/commit/bda6732))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.75.0](https://github.com/lobehub/lobe-chat/compare/v0.74.0...v0.75.0)

<sup>Released on **2023-09-11**</sup>

#### ♻ Code Refactoring

- **misc**: Fefactor index url fetch.

#### ✨ Features

- **market**: Add prompt token count.
- **misc**: Add agents market and improve UI components, Add and refactor components for chat input feature, Add functions for generating and analyzing JSON files, generating resource files and table of contents, and formatting console output, Add new settings for Azure OpenAI and OpenAI in locales files, Add new string, create AgentModal component, implement GridCardItem and Loading components, import AgentModal, Add SideBar component, new actions, and update market store state and selectors, Add translations and new setting to "setting.json", Improve functionality and user interface of market page, Modify market features components and update CSS styles, support add agent to chat.

#### 🐛 Bug Fixes

- **misc**: Fix fetcher, Fix market sidebar scroll and add i18n.

#### 💄 Styles

- **misc**: Update loading style and compatible with unknown agent identifier.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Fefactor index url fetch ([257584b](https://github.com/lobehub/lobe-chat/commit/257584b))

#### What's improved

- **market**: Add prompt token count ([16221a7](https://github.com/lobehub/lobe-chat/commit/16221a7))
- **misc**: Add agents market and improve UI components ([116c136](https://github.com/lobehub/lobe-chat/commit/116c136))
- **misc**: Add and refactor components for chat input feature ([f1ac9fe](https://github.com/lobehub/lobe-chat/commit/f1ac9fe))
- **misc**: Add functions for generating and analyzing JSON files, generating resource files and table of contents, and formatting console output ([d7c2e74](https://github.com/lobehub/lobe-chat/commit/d7c2e74))
- **misc**: Add new settings for Azure OpenAI and OpenAI in locales files ([e9e25b5](https://github.com/lobehub/lobe-chat/commit/e9e25b5))
- **misc**: Add new string, create AgentModal component, implement GridCardItem and Loading components, import AgentModal ([2a0e59f](https://github.com/lobehub/lobe-chat/commit/2a0e59f))
- **misc**: Add SideBar component, new actions, and update market store state and selectors ([8f6cfda](https://github.com/lobehub/lobe-chat/commit/8f6cfda))
- **misc**: Add translations and new setting to "setting.json" ([aca3822](https://github.com/lobehub/lobe-chat/commit/aca3822))
- **misc**: Improve functionality and user interface of market page ([1d465d6](https://github.com/lobehub/lobe-chat/commit/1d465d6))
- **misc**: Modify market features components and update CSS styles ([97e4179](https://github.com/lobehub/lobe-chat/commit/97e4179))
- **misc**: Support add agent to chat ([3b930c4](https://github.com/lobehub/lobe-chat/commit/3b930c4))

#### What's fixed

- **misc**: Fix fetcher ([171b2da](https://github.com/lobehub/lobe-chat/commit/171b2da))
- **misc**: Fix market sidebar scroll and add i18n ([9c897d2](https://github.com/lobehub/lobe-chat/commit/9c897d2))

#### Styles

- **misc**: Update loading style and compatible with unknown agent identifier ([2e2231d](https://github.com/lobehub/lobe-chat/commit/2e2231d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.74.0](https://github.com/lobehub/lobe-chat/compare/v0.73.0...v0.74.0)

<sup>Released on **2023-09-11**</sup>

#### ✨ Features

- **misc**: Add russian locally, Update Russian and English locally (LLM tab).

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add russian locally ([7b67c9f](https://github.com/lobehub/lobe-chat/commit/7b67c9f))
- **misc**: Update Russian and English locally (LLM tab) ([3b23e70](https://github.com/lobehub/lobe-chat/commit/3b23e70))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.73.0](https://github.com/lobehub/lobe-chat/compare/v0.72.4...v0.73.0)

<sup>Released on **2023-09-10**</sup>

#### ✨ Features

- **misc**: Support Azure OpenAI.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support Azure OpenAI, closes [#177](https://github.com/lobehub/lobe-chat/issues/177) ([f0c9532](https://github.com/lobehub/lobe-chat/commit/f0c9532))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.72.4](https://github.com/lobehub/lobe-chat/compare/v0.72.3...v0.72.4)

<sup>Released on **2023-09-10**</sup>

#### 🐛 Bug Fixes

- **misc**: Use en-US when no suit lang with plugin index.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Use en-US when no suit lang with plugin index ([4e9668d](https://github.com/lobehub/lobe-chat/commit/4e9668d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.72.3](https://github.com/lobehub/lobe-chat/compare/v0.72.2...v0.72.3)

<sup>Released on **2023-09-09**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix sessionList double click on mobile.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix sessionList double click on mobile, closes [#169](https://github.com/lobehub/lobe-chat/issues/169) ([3ea2bce](https://github.com/lobehub/lobe-chat/commit/3ea2bce))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.72.2](https://github.com/lobehub/lobe-chat/compare/v0.72.1...v0.72.2)

<sup>Released on **2023-09-09**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix mobile switch when session selected.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix mobile switch when session selected, closes [#167](https://github.com/lobehub/lobe-chat/issues/167) ([40d8a11](https://github.com/lobehub/lobe-chat/commit/40d8a11))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.72.1](https://github.com/lobehub/lobe-chat/compare/v0.72.0...v0.72.1)

<sup>Released on **2023-09-09**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正异步水合造成的初始状态不稳定的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正异步水合造成的初始状态不稳定的问题 ([2208f8a](https://github.com/lobehub/lobe-chat/commit/2208f8a))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.72.0](https://github.com/lobehub/lobe-chat/compare/v0.71.1...v0.72.0)

<sup>Released on **2023-09-09**</sup>

#### ✨ Features

- **misc**: Add plugin market Setting Modal, 支持快速刷新与预览 manifest, 适配插件 i18n 方案.

#### 🐛 Bug Fixes

- **misc**: 修正删除插件时错误开启的问题.

#### 💄 Styles

- **misc**: 优化 manifest 预览的尺寸.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add plugin market Setting Modal ([a0603a9](https://github.com/lobehub/lobe-chat/commit/a0603a9))
- **misc**: 支持快速刷新与预览 manifest, closes [#150](https://github.com/lobehub/lobe-chat/issues/150) ([5bd2eb0](https://github.com/lobehub/lobe-chat/commit/5bd2eb0))
- **misc**: 适配插件 i18n 方案 ([8709ab3](https://github.com/lobehub/lobe-chat/commit/8709ab3))

#### What's fixed

- **misc**: 修正删除插件时错误开启的问题 ([0e35c18](https://github.com/lobehub/lobe-chat/commit/0e35c18))

#### Styles

- **misc**: 优化 manifest 预览的尺寸 ([27f8d6d](https://github.com/lobehub/lobe-chat/commit/27f8d6d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.71.1](https://github.com/lobehub/lobe-chat/compare/v0.71.0...v0.71.1)

<sup>Released on **2023-09-09**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix mobile route.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix mobile route, closes [#165](https://github.com/lobehub/lobe-chat/issues/165) ([d5e03b6](https://github.com/lobehub/lobe-chat/commit/d5e03b6))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.71.0](https://github.com/lobehub/lobe-chat/compare/v0.70.4...v0.71.0)

<sup>Released on **2023-09-09**</sup>

#### ✨ Features

- **misc**: Migrate localStorage to indexedDB.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Migrate localStorage to indexedDB, closes [#160](https://github.com/lobehub/lobe-chat/issues/160) ([7f96deb](https://github.com/lobehub/lobe-chat/commit/7f96deb))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.70.4](https://github.com/lobehub/lobe-chat/compare/v0.70.3...v0.70.4)

<sup>Released on **2023-09-09**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix route.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix route ([2d1e8d6](https://github.com/lobehub/lobe-chat/commit/2d1e8d6))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.70.3](https://github.com/lobehub/lobe-chat/compare/v0.70.2...v0.70.3)

<sup>Released on **2023-09-09**</sup>

#### 💄 Styles

- **misc**: Better mobile style.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Better mobile style ([776c407](https://github.com/lobehub/lobe-chat/commit/776c407))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.70.2](https://github.com/lobehub/lobe-chat/compare/v0.70.1...v0.70.2)

<sup>Released on **2023-09-08**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正移动端路由问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正移动端路由问题 ([ae3d2f4](https://github.com/lobehub/lobe-chat/commit/ae3d2f4))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.70.1](https://github.com/lobehub/lobe-chat/compare/v0.70.0...v0.70.1)

<sup>Released on **2023-09-08**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor settingsSelectors to globalSelectors.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor settingsSelectors to globalSelectors ([38917e8](https://github.com/lobehub/lobe-chat/commit/38917e8))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.70.0](https://github.com/lobehub/lobe-chat/compare/v0.69.1...v0.70.0)

<sup>Released on **2023-09-08**</sup>

#### ✨ Features

- **misc**: Refactor to url state.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Refactor to url state, closes [#157](https://github.com/lobehub/lobe-chat/issues/157) ([2efac2b](https://github.com/lobehub/lobe-chat/commit/2efac2b))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.69.1](https://github.com/lobehub/lobe-chat/compare/v0.69.0...v0.69.1)

<sup>Released on **2023-09-06**</sup>

#### ♻ Code Refactoring

- **misc**: Migrate openai-edge to openai.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Migrate openai-edge to openai, closes [#145](https://github.com/lobehub/lobe-chat/issues/145) ([75ee574](https://github.com/lobehub/lobe-chat/commit/75ee574))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.69.0](https://github.com/lobehub/lobe-chat/compare/v0.68.1...v0.69.0)

<sup>Released on **2023-09-06**</sup>

#### ✨ Features

- **misc**: Add new import statement for "Flexbox" component in "Empty" component.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add new import statement for "Flexbox" component in "Empty" component ([68db626](https://github.com/lobehub/lobe-chat/commit/68db626))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.68.1](https://github.com/lobehub/lobe-chat/compare/v0.68.0...v0.68.1)

<sup>Released on **2023-09-03**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正数组合并逻辑，修正被移除插件无法看到的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正数组合并逻辑 ([e36e621](https://github.com/lobehub/lobe-chat/commit/e36e621))
- **misc**: 修正被移除插件无法看到的问题 ([c17eb56](https://github.com/lobehub/lobe-chat/commit/c17eb56))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.68.0](https://github.com/lobehub/lobe-chat/compare/v0.67.0...v0.68.0)

<sup>Released on **2023-09-03**</sup>

#### ✨ Features

- **misc**: Plugin default use iframe render.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Plugin default use iframe render, closes [#141](https://github.com/lobehub/lobe-chat/issues/141) ([35a3a16](https://github.com/lobehub/lobe-chat/commit/35a3a16))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.67.0](https://github.com/lobehub/lobe-chat/compare/v0.66.0...v0.67.0)

<sup>Released on **2023-09-02**</sup>

#### ♻ Code Refactoring

- **plugin**: 重构 plugin Store 组织结构，便于开发与迭代维护.

#### ✨ Features

- **plugin-dev**: 优化 manifest 报错原因提示，并支持 id 从 manifest 自动获取.

#### 🐛 Bug Fixes

- **plugin-dev**: 修正编辑模式下预览展示问题和 id 重复校验问题.
- **plugin**: 修正开启插件后会话无效的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **plugin**: 重构 plugin Store 组织结构，便于开发与迭代维护 ([ec527cb](https://github.com/lobehub/lobe-chat/commit/ec527cb))

#### What's improved

- **plugin-dev**: 优化 manifest 报错原因提示，并支持 id 从 manifest 自动获取 ([7f0787d](https://github.com/lobehub/lobe-chat/commit/7f0787d))

#### What's fixed

- **plugin-dev**: 修正编辑模式下预览展示问题和 id 重复校验问题 ([17c39ef](https://github.com/lobehub/lobe-chat/commit/17c39ef))
- **plugin**: 修正开启插件后会话无效的问题 ([82e3beb](https://github.com/lobehub/lobe-chat/commit/82e3beb))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.66.0](https://github.com/lobehub/lobe-chat/compare/v0.65.1...v0.66.0)

<sup>Released on **2023-09-02**</sup>

#### ✨ Features

- **misc**: Add russian locally.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add russian locally, closes [#137](https://github.com/lobehub/lobe-chat/issues/137) ([785d50f](https://github.com/lobehub/lobe-chat/commit/785d50f))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.65.1](https://github.com/lobehub/lobe-chat/compare/v0.65.0...v0.65.1)

<sup>Released on **2023-09-01**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正 defaultAgent 无法正常变更的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正 defaultAgent 无法正常变更的问题 ([788d94b](https://github.com/lobehub/lobe-chat/commit/788d94b))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.65.0](https://github.com/lobehub/lobe-chat/compare/v0.64.1...v0.65.0)

<sup>Released on **2023-08-29**</sup>

#### ✨ Features

- **misc**: 支持本地插件自定义 gateway.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持本地插件自定义 gateway, closes [#129](https://github.com/lobehub/lobe-chat/issues/129) ([770048a](https://github.com/lobehub/lobe-chat/commit/770048a))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.64.1](https://github.com/lobehub/lobe-chat/compare/v0.64.0...v0.64.1)

<sup>Released on **2023-08-29**</sup>

#### 💄 Styles

- **misc**: Update i18n.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update i18n, closes [#128](https://github.com/lobehub/lobe-chat/issues/128) ([3bf1509](https://github.com/lobehub/lobe-chat/commit/3bf1509))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.64.0](https://github.com/lobehub/lobe-chat/compare/v0.63.3...v0.64.0)

<sup>Released on **2023-08-29**</sup>

#### ♻ Code Refactoring

- **misc**: Remove no need i18n.

#### ✨ Features

- **misc**: 增加自定义插件的增删改配置功能，完善自定义插件表单的校验逻辑，支持本地插件侧的请求与错误呈现，新增插件配置 Dev 弹窗，绑定本地插件的增删改逻辑.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Remove no need i18n ([808a86a](https://github.com/lobehub/lobe-chat/commit/808a86a))

#### What's improved

- **misc**: 增加自定义插件的增删改配置功能 ([faba081](https://github.com/lobehub/lobe-chat/commit/faba081))
- **misc**: 完善自定义插件表单的校验逻辑 ([4e1fd28](https://github.com/lobehub/lobe-chat/commit/4e1fd28))
- **misc**: 支持本地插件侧的请求与错误呈现 ([7e2b39a](https://github.com/lobehub/lobe-chat/commit/7e2b39a))
- **misc**: 新增插件配置 Dev 弹窗 ([20269b7](https://github.com/lobehub/lobe-chat/commit/20269b7))
- **misc**: 绑定本地插件的增删改逻辑 ([902e7ed](https://github.com/lobehub/lobe-chat/commit/902e7ed))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.63.3](https://github.com/lobehub/lobe-chat/compare/v0.63.2...v0.63.3)

<sup>Released on **2023-08-28**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor with new market url.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor with new market url, closes [#123](https://github.com/lobehub/lobe-chat/issues/123) ([34a88f8](https://github.com/lobehub/lobe-chat/commit/34a88f8))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.63.2](https://github.com/lobehub/lobe-chat/compare/v0.63.1...v0.63.2)

<sup>Released on **2023-08-27**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor AgentSettings.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor AgentSettings, closes [#121](https://github.com/lobehub/lobe-chat/issues/121) ([1f29199](https://github.com/lobehub/lobe-chat/commit/1f29199))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.63.1](https://github.com/lobehub/lobe-chat/compare/v0.63.0...v0.63.1)

<sup>Released on **2023-08-27**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the selectors import.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the selectors import, closes [#120](https://github.com/lobehub/lobe-chat/issues/120) ([6646502](https://github.com/lobehub/lobe-chat/commit/6646502))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.63.0](https://github.com/lobehub/lobe-chat/compare/v0.62.1...v0.63.0)

<sup>Released on **2023-08-27**</sup>

#### ✨ Features

- **misc**: support sharing to shareGPT.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: support sharing to shareGPT, closes [#119](https://github.com/lobehub/lobe-chat/issues/119) ([026e9ec](https://github.com/lobehub/lobe-chat/commit/026e9ec))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.62.1](https://github.com/lobehub/lobe-chat/compare/v0.62.0...v0.62.1)

<sup>Released on **2023-08-26**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix plugin settings error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix plugin settings error, closes [#117](https://github.com/lobehub/lobe-chat/issues/117) ([064d90e](https://github.com/lobehub/lobe-chat/commit/064d90e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.62.0](https://github.com/lobehub/lobe-chat/compare/v0.61.0...v0.62.0)

<sup>Released on **2023-08-26**</sup>

#### ✨ Features

- **misc**: 支持超过 4k 的会话使用 16k 总结标题.

#### 🐛 Bug Fixes

- **misc**: Fix plugin settings error.

#### 💄 Styles

- **misc**: 优化清理会话的操作路径，优化默认角色的配置.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持超过 4k 的会话使用 16k 总结标题 ([5764cfb](https://github.com/lobehub/lobe-chat/commit/5764cfb))

#### What's fixed

- **misc**: Fix plugin settings error ([008c2e3](https://github.com/lobehub/lobe-chat/commit/008c2e3))

#### Styles

- **misc**: 优化清理会话的操作路径 ([6b7218e](https://github.com/lobehub/lobe-chat/commit/6b7218e))
- **misc**: 优化默认角色的配置 ([a07d7a8](https://github.com/lobehub/lobe-chat/commit/a07d7a8))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.61.0](https://github.com/lobehub/lobe-chat/compare/v0.60.4...v0.61.0)

<sup>Released on **2023-08-26**</sup>

#### ✨ Features

- **misc**: 新增自动滚动.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 新增自动滚动，closes [#113](https://github.com/lobehub/lobe-chat/issues/113) ([03fd161](https://github.com/lobehub/lobe-chat/commit/03fd161))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.60.4](https://github.com/lobehub/lobe-chat/compare/v0.60.3...v0.60.4)

<sup>Released on **2023-08-26**</sup>

#### 💄 Styles

- **misc**: 优化文案.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 优化文案 ([9a1e004](https://github.com/lobehub/lobe-chat/commit/9a1e004))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.60.3](https://github.com/lobehub/lobe-chat/compare/v0.60.2...v0.60.3)

<sup>Released on **2023-08-26**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix global state merge error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix global state merge error ([cbc2fc8](https://github.com/lobehub/lobe-chat/commit/cbc2fc8))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.60.2](https://github.com/lobehub/lobe-chat/compare/v0.60.1...v0.60.2)

<sup>Released on **2023-08-26**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix fetch plugin header error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix fetch plugin header error ([fa4a0e1](https://github.com/lobehub/lobe-chat/commit/fa4a0e1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.60.1](https://github.com/lobehub/lobe-chat/compare/v0.60.0...v0.60.1)

<sup>Released on **2023-08-26**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix settings storage error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix settings storage error ([57d7eb1](https://github.com/lobehub/lobe-chat/commit/57d7eb1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.60.0](https://github.com/lobehub/lobe-chat/compare/v0.59.0...v0.60.0)

<sup>Released on **2023-08-26**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor with new market index url.

#### ✨ Features

- **misc**: 支持插件 manifest 加载失败后重试.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor with new market index url ([d2834b7](https://github.com/lobehub/lobe-chat/commit/d2834b7))

#### What's improved

- **misc**: 支持插件 manifest 加载失败后重试 ([f36378e](https://github.com/lobehub/lobe-chat/commit/f36378e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.59.0](https://github.com/lobehub/lobe-chat/compare/v0.58.0...v0.59.0)

<sup>Released on **2023-08-26**</sup>

#### ✨ Features

- **misc**: 支持展示插件插件状态，支持插件 i18n 模式展示.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持展示插件插件状态 ([7e916ac](https://github.com/lobehub/lobe-chat/commit/7e916ac))
- **misc**: 支持插件 i18n 模式展示 ([8614734](https://github.com/lobehub/lobe-chat/commit/8614734))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.58.0](https://github.com/lobehub/lobe-chat/compare/v0.57.0...v0.58.0)

<sup>Released on **2023-08-26**</sup>

#### ✨ Features

- **misc**: Implement responsive design for mobile devices.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Implement responsive design for mobile devices, closes [#95](https://github.com/lobehub/lobe-chat/issues/95) ([fdb3c93](https://github.com/lobehub/lobe-chat/commit/fdb3c93))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.57.0](https://github.com/lobehub/lobe-chat/compare/v0.56.0...v0.57.0)

<sup>Released on **2023-08-26**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor to ChatErrorType.

#### ✨ Features

- **misc**: 完善插件请求的错误处理，支持修改与记录插件的配置，支持发送插件配置信息，支持渲染 manifest 中的 settings, 支持设置不正确时进行插件的配置，新增插件请求状态的错误处理.

#### 🐛 Bug Fixes

- **misc**: 修正缓存旧数据的报错问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor to ChatErrorType ([cd1a033](https://github.com/lobehub/lobe-chat/commit/cd1a033))

#### What's improved

- **misc**: 完善插件请求的错误处理 ([0698d89](https://github.com/lobehub/lobe-chat/commit/0698d89))
- **misc**: 支持修改与记录插件的配置 ([76e8237](https://github.com/lobehub/lobe-chat/commit/76e8237))
- **misc**: 支持发送插件配置信息 ([2cedc85](https://github.com/lobehub/lobe-chat/commit/2cedc85))
- **misc**: 支持渲染 manifest 中的 settings ([1185300](https://github.com/lobehub/lobe-chat/commit/1185300))
- **misc**: 支持设置不正确时进行插件的配置 ([f972481](https://github.com/lobehub/lobe-chat/commit/f972481))
- **misc**: 新增插件请求状态的错误处理 ([228002a](https://github.com/lobehub/lobe-chat/commit/228002a))

#### What's fixed

- **misc**: 修正缓存旧数据的报错问题 ([5d8008f](https://github.com/lobehub/lobe-chat/commit/5d8008f))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.56.0](https://github.com/lobehub/lobe-chat/compare/v0.55.1...v0.56.0)

<sup>Released on **2023-08-24**</sup>

#### ✨ Features

- **misc**: Use new plugin manifest to support plugin’s multi api.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Use new plugin manifest to support plugin’s multi api, closes [#101](https://github.com/lobehub/lobe-chat/issues/101) ([4534598](https://github.com/lobehub/lobe-chat/commit/4534598))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.55.1](https://github.com/lobehub/lobe-chat/compare/v0.55.0...v0.55.1)

<sup>Released on **2023-08-22**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor plugin api with @lobehub/chat-plugins-gateway.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor plugin api with @lobehub/chat-plugins-gateway, closes [#100](https://github.com/lobehub/lobe-chat/issues/100) ([b88d0db](https://github.com/lobehub/lobe-chat/commit/b88d0db))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.55.0](https://github.com/lobehub/lobe-chat/compare/v0.54.4...v0.55.0)

<sup>Released on **2023-08-22**</sup>

#### ♻ Code Refactoring

- **misc**: 将网关实现代码集成进 Chat 本体，抽取插件为独立 store, 重构 openai 接口调用逻辑，将插件 schema 开启关闭逻辑与接口解耦，重构插件列表获取逻辑，进而完全移除 plugins 目录.

#### ✨ Features

- **misc**: 初步完成插件市场动态加载全链路，实现插件组件的动态加载.

#### 🐛 Bug Fixes

- **misc**: Fix error, 修正无法正常开启插件的问题，修正测试，补充插件 store 的水合逻辑.

#### 💄 Styles

- **misc**: 完成插件市场 loading 态样式.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 将网关实现代码集成进 Chat 本体 ([17e8161](https://github.com/lobehub/lobe-chat/commit/17e8161))
- **misc**: 抽取插件为独立 store ([12b7e7d](https://github.com/lobehub/lobe-chat/commit/12b7e7d))
- **misc**: 重构 openai 接口调用逻辑，将插件 schema 开启关闭逻辑与接口解耦 ([5aa886e](https://github.com/lobehub/lobe-chat/commit/5aa886e))
- **misc**: 重构插件列表获取逻辑，进而完全移除 plugins 目录 ([10055e1](https://github.com/lobehub/lobe-chat/commit/10055e1))

#### What's improved

- **misc**: 初步完成插件市场动态加载全链路 ([bc5e40f](https://github.com/lobehub/lobe-chat/commit/bc5e40f))
- **misc**: 实现插件组件的动态加载 ([04dbab2](https://github.com/lobehub/lobe-chat/commit/04dbab2))

#### What's fixed

- **misc**: Fix error ([fbeec75](https://github.com/lobehub/lobe-chat/commit/fbeec75))
- **misc**: 修正无法正常开启插件的问题 ([b3e9090](https://github.com/lobehub/lobe-chat/commit/b3e9090))
- **misc**: 修正测试 ([001de5b](https://github.com/lobehub/lobe-chat/commit/001de5b))
- **misc**: 补充插件 store 的水合逻辑 ([bfb649b](https://github.com/lobehub/lobe-chat/commit/bfb649b))

#### Styles

- **misc**: 完成插件市场 loading 态样式 ([8009691](https://github.com/lobehub/lobe-chat/commit/8009691))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.54.4](https://github.com/lobehub/lobe-chat/compare/v0.54.3...v0.54.4)

<sup>Released on **2023-08-21**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix not cannot change setting error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix not cannot change setting error, closes [#86](https://github.com/lobehub/lobe-chat/issues/86) ([6405c28](https://github.com/lobehub/lobe-chat/commit/6405c28))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.54.3](https://github.com/lobehub/lobe-chat/compare/v0.54.2...v0.54.3)

<sup>Released on **2023-08-21**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor plugin request.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor plugin request, closes [#89](https://github.com/lobehub/lobe-chat/issues/89) ([23efee3](https://github.com/lobehub/lobe-chat/commit/23efee3))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.54.2](https://github.com/lobehub/lobe-chat/compare/v0.54.1...v0.54.2)

<sup>Released on **2023-08-16**</sup>

#### 💄 Styles

- **misc**: 修正图片选项的样式问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 修正图片选项的样式问题 ([5f576cb](https://github.com/lobehub/lobe-chat/commit/5f576cb))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.54.1](https://github.com/lobehub/lobe-chat/compare/v0.54.0...v0.54.1)

<sup>Released on **2023-08-16**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正 i18n 失效的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正 i18n 失效的问题，closes [#80](https://github.com/lobehub/lobe-chat/issues/80) ([b8d957b](https://github.com/lobehub/lobe-chat/commit/b8d957b))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.54.0](https://github.com/lobehub/lobe-chat/compare/v0.53.0...v0.54.0)

<sup>Released on **2023-08-15**</sup>

#### ✨ Features

- **misc**: Add new features and improve user interface and functionality.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add new features and improve user interface and functionality ([1543bd1](https://github.com/lobehub/lobe-chat/commit/1543bd1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.53.0](https://github.com/lobehub/lobe-chat/compare/v0.52.1...v0.53.0)

<sup>Released on **2023-08-15**</sup>

#### ✨ Features

- **sidebar**: Add DraggablePanelContainer and adjust layout and styling.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **sidebar**: Add DraggablePanelContainer and adjust layout and styling ([e8c384f](https://github.com/lobehub/lobe-chat/commit/e8c384f))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.52.1](https://github.com/lobehub/lobe-chat/compare/v0.52.0...v0.52.1)

<sup>Released on **2023-08-15**</sup>

#### ♻ Code Refactoring

- **misc**: Replace cdn.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Replace cdn ([2875400](https://github.com/lobehub/lobe-chat/commit/2875400))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.52.0](https://github.com/lobehub/lobe-chat/compare/v0.51.0...v0.52.0)

<sup>Released on **2023-08-15**</sup>

#### ✨ Features

- **misc**: Add avatar compress.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add avatar compress ([1325b40](https://github.com/lobehub/lobe-chat/commit/1325b40))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.51.0](https://github.com/lobehub/lobe-chat/compare/v0.50.0...v0.51.0)

<sup>Released on **2023-08-15**</sup>

#### ✨ Features

- **misc**: Add Footer component and modify Token and index files.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add Footer component and modify Token and index files ([41a3823](https://github.com/lobehub/lobe-chat/commit/41a3823))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.50.0](https://github.com/lobehub/lobe-chat/compare/v0.49.0...v0.50.0)

<sup>Released on **2023-08-15**</sup>

#### ✨ Features

- **misc**: Update messages, settings, error codes, plugin names, weather data display, and UI.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Update messages, settings, error codes, plugin names, weather data display, and UI ([a41db51](https://github.com/lobehub/lobe-chat/commit/a41db51))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.49.0](https://github.com/lobehub/lobe-chat/compare/v0.48.0...v0.49.0)

<sup>Released on **2023-08-15**</sup>

#### ✨ Features

- **misc**: Add `BackToBottom` to conversation, Update icons and text in various components.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add `BackToBottom` to conversation ([1433aa9](https://github.com/lobehub/lobe-chat/commit/1433aa9))
- **misc**: Update icons and text in various components ([0e7a683](https://github.com/lobehub/lobe-chat/commit/0e7a683))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.48.0](https://github.com/lobehub/lobe-chat/compare/v0.47.0...v0.48.0)

<sup>Released on **2023-08-15**</sup>

#### ✨ Features

- **misc**: Import SiOpenai icon and replace 'Tag' component in chat feature.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Import SiOpenai icon and replace 'Tag' component in chat feature ([98b0352](https://github.com/lobehub/lobe-chat/commit/98b0352))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.47.0](https://github.com/lobehub/lobe-chat/compare/v0.46.1...v0.47.0)

<sup>Released on **2023-08-15**</sup>

#### ✨ Features

- **misc**: Add and update UI elements and agent configuration.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add and update UI elements and agent configuration ([eb7fbee](https://github.com/lobehub/lobe-chat/commit/eb7fbee))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.46.1](https://github.com/lobehub/lobe-chat/compare/v0.46.0...v0.46.1)

<sup>Released on **2023-08-14**</sup>

#### 💄 Styles

- **misc**: Fix SystemRole Skeleton padding.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix SystemRole Skeleton padding ([ce485a0](https://github.com/lobehub/lobe-chat/commit/ce485a0))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.46.0](https://github.com/lobehub/lobe-chat/compare/v0.45.0...v0.46.0)

<sup>Released on **2023-08-14**</sup>

#### ✨ Features

- **misc**: Update styling and functionality of AgentPrompt and EditableMessage components, 支持停止生成消息.

#### 🐛 Bug Fixes

- **misc**: Remove input highlight.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Update styling and functionality of AgentPrompt and EditableMessage components ([80b521c](https://github.com/lobehub/lobe-chat/commit/80b521c))
- **misc**: 支持停止生成消息，closes [#78](https://github.com/lobehub/lobe-chat/issues/78) ([9eeca80](https://github.com/lobehub/lobe-chat/commit/9eeca80))

#### What's fixed

- **misc**: Remove input highlight ([ad2001a](https://github.com/lobehub/lobe-chat/commit/ad2001a))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.45.0](https://github.com/lobehub/lobe-chat/compare/v0.44.4...v0.45.0)

<sup>Released on **2023-08-14**</sup>

#### ✨ Features

- **misc**: 优化每个角色的初始引导.

#### 💄 Styles

- **misc**: 优化初始化加载状态，等到会话加载完毕再显示内容.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 优化每个角色的初始引导，closes [#76](https://github.com/lobehub/lobe-chat/issues/76) ([8d78dc5](https://github.com/lobehub/lobe-chat/commit/8d78dc5))

#### Styles

- **misc**: 优化初始化加载状态，等到会话加载完毕再显示内容 ([cf603cb](https://github.com/lobehub/lobe-chat/commit/cf603cb))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.44.4](https://github.com/lobehub/lobe-chat/compare/v0.44.3...v0.44.4)

<sup>Released on **2023-08-13**</sup>

#### 💄 Styles

- **misc**: 优化 Chat Skeleton 样式，优化 Inbox 样式.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 优化 Chat Skeleton 样式 ([3f83be0](https://github.com/lobehub/lobe-chat/commit/3f83be0))
- **misc**: 优化 Inbox 样式 ([924c12e](https://github.com/lobehub/lobe-chat/commit/924c12e))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.44.3](https://github.com/lobehub/lobe-chat/compare/v0.44.2...v0.44.3)

<sup>Released on **2023-08-13**</sup>

#### ♻ Code Refactoring

- **misc**: 重构 organizeChats 方法.

#### 🐛 Bug Fixes

- **misc**: 修正 inbox 点击重新生成会报错的问题.

#### 💄 Styles

- **misc**: 修正话题列表无法滚动的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 重构 organizeChats 方法 ([799612e](https://github.com/lobehub/lobe-chat/commit/799612e))

#### What's fixed

- **misc**: 修正 inbox 点击重新生成会报错的问题 ([064ef56](https://github.com/lobehub/lobe-chat/commit/064ef56))

#### Styles

- **misc**: 修正话题列表无法滚动的问题 ([26772e7](https://github.com/lobehub/lobe-chat/commit/26772e7))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.44.2](https://github.com/lobehub/lobe-chat/compare/v0.44.1...v0.44.2)

<sup>Released on **2023-08-13**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正重新生成时切分历史消息的逻辑.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正重新生成时切分历史消息的逻辑，closes [#50](https://github.com/lobehub/lobe-chat/issues/50) ([de5141f](https://github.com/lobehub/lobe-chat/commit/de5141f))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.44.1](https://github.com/lobehub/lobe-chat/compare/v0.44.0...v0.44.1)

<sup>Released on **2023-08-12**</sup>

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>
</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.44.0](https://github.com/lobehub/lobe-chat/compare/v0.43.0...v0.44.0)

<sup>Released on **2023-08-12**</sup>

#### ♻ Code Refactoring

- **misc**: 优化 Inbox 会话的实现逻辑，将 chat 中的功能模型拆分到 features 中，重构 session 相关实现，移除循环依赖.

#### ✨ Features

- **misc**: 支持 inbox 消息导出，支持 inbox 的会话功能，新增 inbox 数据模型，新增 inbox 模块入口.

#### 💄 Styles

- **misc**: Fix Inbox defaultMessage avatar, 优化 header 的 setting 展示，优化门禁下默认的解锁方式，补充 ChatList 的 Loading 态.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 优化 Inbox 会话的实现逻辑 ([22cc4cf](https://github.com/lobehub/lobe-chat/commit/22cc4cf))
- **misc**: 将 chat 中的功能模型拆分到 features 中 ([e25a856](https://github.com/lobehub/lobe-chat/commit/e25a856))
- **misc**: 重构 session 相关实现，移除循环依赖 ([9acf65c](https://github.com/lobehub/lobe-chat/commit/9acf65c))

#### What's improved

- **misc**: 支持 inbox 消息导出 ([498e075](https://github.com/lobehub/lobe-chat/commit/498e075))
- **misc**: 支持 inbox 的会话功能 ([9b713b8](https://github.com/lobehub/lobe-chat/commit/9b713b8))
- **misc**: 新增 inbox 数据模型 ([91a8158](https://github.com/lobehub/lobe-chat/commit/91a8158))
- **misc**: 新增 inbox 模块入口 ([6fc8907](https://github.com/lobehub/lobe-chat/commit/6fc8907))

#### Styles

- **misc**: Fix Inbox defaultMessage avatar ([dbc18a4](https://github.com/lobehub/lobe-chat/commit/dbc18a4))
- **misc**: 优化 header 的 setting 展示 ([201d380](https://github.com/lobehub/lobe-chat/commit/201d380))
- **misc**: 优化门禁下默认的解锁方式 ([df9bb45](https://github.com/lobehub/lobe-chat/commit/df9bb45))
- **misc**: 补充 ChatList 的 Loading 态 ([eb3eb5d](https://github.com/lobehub/lobe-chat/commit/eb3eb5d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.43.0](https://github.com/lobehub/lobe-chat/compare/v0.42.3...v0.43.0)

<sup>Released on **2023-08-12**</sup>

#### ✨ Features

- **misc**: 支持切换语言.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持切换语言，closes [#67](https://github.com/lobehub/lobe-chat/issues/67) ([63ed8ec](https://github.com/lobehub/lobe-chat/commit/63ed8ec))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.42.3](https://github.com/lobehub/lobe-chat/compare/v0.42.2...v0.42.3)

<sup>Released on **2023-08-12**</sup>

#### 💄 Styles

- **misc**: 暂时隐藏 Hero 模板.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 暂时隐藏 Hero 模板 ([8289ae6](https://github.com/lobehub/lobe-chat/commit/8289ae6))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.42.2](https://github.com/lobehub/lobe-chat/compare/v0.42.1...v0.42.2)

<sup>Released on **2023-08-12**</sup>

#### ♻ Code Refactoring

- **misc**: 将 useSettings 更名为 useGlobalStore, 将原本的 settings 更名为 global, 收敛切换 SideBar 方法为 useSwitchSideBarOnInit, 重构需本地缓存的状态为 preference.

#### 🐛 Bug Fixes

- **misc**: 修正移除 session 时的路由跳转逻辑.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 将 useSettings 更名为 useGlobalStore ([bdde7df](https://github.com/lobehub/lobe-chat/commit/bdde7df))
- **misc**: 将原本的 settings 更名为 global ([e42d34c](https://github.com/lobehub/lobe-chat/commit/e42d34c))
- **misc**: 收敛切换 SideBar 方法为 useSwitchSideBarOnInit ([bbad38f](https://github.com/lobehub/lobe-chat/commit/bbad38f))
- **misc**: 重构需本地缓存的状态为 preference ([8359b62](https://github.com/lobehub/lobe-chat/commit/8359b62))

#### What's fixed

- **misc**: 修正移除 session 时的路由跳转逻辑 ([8b7838d](https://github.com/lobehub/lobe-chat/commit/8b7838d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.42.1](https://github.com/lobehub/lobe-chat/compare/v0.42.0...v0.42.1)

<sup>Released on **2023-08-12**</sup>

#### 💄 Styles

- **misc**: 优化 App 首页 Loading 态.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 优化 App 首页 Loading 态 ([72104e8](https://github.com/lobehub/lobe-chat/commit/72104e8))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.42.0](https://github.com/lobehub/lobe-chat/compare/v0.41.2...v0.42.0)

<sup>Released on **2023-08-11**</sup>

#### ✨ Features

- **misc**: Add `Welcome` page.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add `Welcome` page, closes [#60](https://github.com/lobehub/lobe-chat/issues/60) ([810ab0f](https://github.com/lobehub/lobe-chat/commit/810ab0f))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.41.2](https://github.com/lobehub/lobe-chat/compare/v0.41.1...v0.41.2)

<sup>Released on **2023-08-10**</sup>

#### ♻ Code Refactoring

- **misc**: 将 sessionStore 默认 equalFn 改为 shallow, 将 settingStore 默认 equalFn 改为 shallow.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 将 sessionStore 默认 equalFn 改为 shallow ([5c1b8d7](https://github.com/lobehub/lobe-chat/commit/5c1b8d7))
- **misc**: 将 settingStore 默认 equalFn 改为 shallow ([1e72308](https://github.com/lobehub/lobe-chat/commit/1e72308))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.41.1](https://github.com/lobehub/lobe-chat/compare/v0.41.0...v0.41.1)

<sup>Released on **2023-08-10**</sup>

#### ♻ Code Refactoring

- **misc**: 重构 settings store 代码写法.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 重构 settings store 代码写法 ([4b6f917](https://github.com/lobehub/lobe-chat/commit/4b6f917))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.41.0](https://github.com/lobehub/lobe-chat/compare/v0.40.7...v0.41.0)

<sup>Released on **2023-08-10**</sup>

#### ✨ Features

- **misc**: 支持持久化隐藏 Topic 功能.

#### 💄 Styles

- **misc**: 优化第一次水合逻辑.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持持久化隐藏 Topic 功能 ([9ea2778](https://github.com/lobehub/lobe-chat/commit/9ea2778))

#### Styles

- **misc**: 优化第一次水合逻辑 ([fefae61](https://github.com/lobehub/lobe-chat/commit/fefae61))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.40.7](https://github.com/lobehub/lobe-chat/compare/v0.40.6...v0.40.7)

<sup>Released on **2023-08-10**</sup>

#### 💄 Styles

- **misc**: 优化 Topic 的水合加载效果.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 优化 Topic 的水合加载效果 ([0cd0088](https://github.com/lobehub/lobe-chat/commit/0cd0088))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.40.6](https://github.com/lobehub/lobe-chat/compare/v0.40.5...v0.40.6)

<sup>Released on **2023-08-10**</sup>

#### ♻ Code Refactoring

- **misc**: 重构优化 hydrated 的判断逻辑.

#### 💄 Styles

- **misc**: 优化水合前的加载效果.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 重构优化 hydrated 的判断逻辑 ([1781119](https://github.com/lobehub/lobe-chat/commit/1781119))

#### Styles

- **misc**: 优化水合前的加载效果 ([6bbd978](https://github.com/lobehub/lobe-chat/commit/6bbd978))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.40.5](https://github.com/lobehub/lobe-chat/compare/v0.40.4...v0.40.5)

<sup>Released on **2023-08-10**</sup>

#### 💄 Styles

- **misc**: 增加未初始化的 loading 态.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 增加未初始化的 loading 态 ([dcb7c07](https://github.com/lobehub/lobe-chat/commit/dcb7c07))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.40.4](https://github.com/lobehub/lobe-chat/compare/v0.40.3...v0.40.4)

<sup>Released on **2023-08-10**</sup>

#### 💄 Styles

- **misc**: 优化 Header 样式.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 优化 Header 样式 ([edd148a](https://github.com/lobehub/lobe-chat/commit/edd148a))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.40.3](https://github.com/lobehub/lobe-chat/compare/v0.40.2...v0.40.3)

<sup>Released on **2023-08-10**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正没有 prompt 的编辑与保存按钮的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正没有 prompt 的编辑与保存按钮的问题 ([b7e1648](https://github.com/lobehub/lobe-chat/commit/b7e1648))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.40.2](https://github.com/lobehub/lobe-chat/compare/v0.40.1...v0.40.2)

<sup>Released on **2023-08-08**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正 defaults 造成的 config 报错.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正 defaults 造成的 config 报错 ([0857fa7](https://github.com/lobehub/lobe-chat/commit/0857fa7))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.40.1](https://github.com/lobehub/lobe-chat/compare/v0.40.0...v0.40.1)

<sup>Released on **2023-08-06**</sup>

#### 🐛 Bug Fixes

- **misc**: 优化 openai 接口的错误处理逻辑.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 优化 openai 接口的错误处理逻辑 ([eae78fe](https://github.com/lobehub/lobe-chat/commit/eae78fe))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.40.0](https://github.com/lobehub/lobe-chat/compare/v0.39.4...v0.40.0)

<sup>Released on **2023-08-05**</sup>

#### ✨ Features

- **misc**: Add new dependency, add Tag and PluginTag components, update HeaderTitle.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add new dependency, add Tag and PluginTag components, update HeaderTitle, closes [#56](https://github.com/lobehub/lobe-chat/issues/56) [#55](https://github.com/lobehub/lobe-chat/issues/55) [#54](https://github.com/lobehub/lobe-chat/issues/54) ([2812ea2](https://github.com/lobehub/lobe-chat/commit/2812ea2))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.39.4](https://github.com/lobehub/lobe-chat/compare/v0.39.3...v0.39.4)

<sup>Released on **2023-08-05**</sup>

#### 💄 Styles

- **misc**: 修正 assistant 消息没有 background 的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 修正 assistant 消息没有 background 的问题，closes [#42](https://github.com/lobehub/lobe-chat/issues/42) ([812e976](https://github.com/lobehub/lobe-chat/commit/812e976))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.39.3](https://github.com/lobehub/lobe-chat/compare/v0.39.2...v0.39.3)

<sup>Released on **2023-08-04**</sup>

#### 🐛 Bug Fixes

- **misc**: 优化 405 报错返回内容，并优化 openai 服务端超时处理逻辑.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 优化 405 报错返回内容，并优化 openai 服务端超时处理逻辑 ([0acc829](https://github.com/lobehub/lobe-chat/commit/0acc829))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.39.2](https://github.com/lobehub/lobe-chat/compare/v0.39.1...v0.39.2)

<sup>Released on **2023-08-04**</sup>

#### 💄 Styles

- **misc**: 优化 topic 样式.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 优化 topic 样式 ([75dc034](https://github.com/lobehub/lobe-chat/commit/75dc034))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.39.1](https://github.com/lobehub/lobe-chat/compare/v0.39.0...v0.39.1)

<sup>Released on **2023-08-04**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正 basePath 在生产环境下不生效的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正 basePath 在生产环境下不生效的问题 ([71b9139](https://github.com/lobehub/lobe-chat/commit/71b9139))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.39.0](https://github.com/lobehub/lobe-chat/compare/v0.38.0...v0.39.0)

<sup>Released on **2023-08-04**</sup>

#### ✨ Features

- **misc**: 支持多轮的插件意图识别，支持自定义 OpenAI 代理地址.

#### 💄 Styles

- **misc**: 优化插件的展示逻辑.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持多轮的插件意图识别 ([5127f1b](https://github.com/lobehub/lobe-chat/commit/5127f1b))
- **misc**: 支持自定义 OpenAI 代理地址 ([33a111c](https://github.com/lobehub/lobe-chat/commit/33a111c))

#### Styles

- **misc**: 优化插件的展示逻辑 ([7621bad](https://github.com/lobehub/lobe-chat/commit/7621bad))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.38.0](https://github.com/lobehub/lobe-chat/compare/v0.37.0...v0.38.0)

<sup>Released on **2023-08-04**</sup>

#### ✨ Features

- **misc**: Add topic empty.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add topic empty ([b9f267c](https://github.com/lobehub/lobe-chat/commit/b9f267c))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.37.0](https://github.com/lobehub/lobe-chat/compare/v0.36.1...v0.37.0)

<sup>Released on **2023-08-03**</sup>

#### ✨ Features

- **misc**: 支持使用全局助手的设置作为默认助手的创建角色.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持使用全局助手的设置作为默认助手的创建角色，closes [#44](https://github.com/lobehub/lobe-chat/issues/44) ([f91857d](https://github.com/lobehub/lobe-chat/commit/f91857d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.36.1](https://github.com/lobehub/lobe-chat/compare/v0.36.0...v0.36.1)

<sup>Released on **2023-08-03**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor zustand usage with v4.4.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor zustand usage with v4.4, closes [#52](https://github.com/lobehub/lobe-chat/issues/52) ([4c65aa7](https://github.com/lobehub/lobe-chat/commit/4c65aa7))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.36.0](https://github.com/lobehub/lobe-chat/compare/v0.35.1...v0.36.0)

<sup>Released on **2023-08-03**</sup>

#### ✨ Features

- **misc**: 实现自定义历史消息数功能.

#### 🐛 Bug Fixes

- **misc**: Fix setting type.

#### 💄 Styles

- **misc**: Fix session item height.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 实现自定义历史消息数功能 ([7baa022](https://github.com/lobehub/lobe-chat/commit/7baa022))

#### What's fixed

- **misc**: Fix setting type ([57e415e](https://github.com/lobehub/lobe-chat/commit/57e415e))

#### Styles

- **misc**: Fix session item height ([6cd1de5](https://github.com/lobehub/lobe-chat/commit/6cd1de5))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.35.1](https://github.com/lobehub/lobe-chat/compare/v0.35.0...v0.35.1)

<sup>Released on **2023-07-31**</sup>

#### 💄 Styles

- **misc**: Update doc mode and token tags.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update doc mode and token tags ([1d3c5b6](https://github.com/lobehub/lobe-chat/commit/1d3c5b6))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.35.0](https://github.com/lobehub/lobe-chat/compare/v0.34.0...v0.35.0)

<sup>Released on **2023-07-31**</sup>

#### ✨ Features

- **misc**: Add agent settings functionality, new components, and features for AgentMeta, Add and modify translations for various keys in JSON code files.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add agent settings functionality, new components, and features for AgentMeta ([b1e5ff9](https://github.com/lobehub/lobe-chat/commit/b1e5ff9))
- **misc**: Add and modify translations for various keys in JSON code files ([503adb4](https://github.com/lobehub/lobe-chat/commit/503adb4))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.34.0](https://github.com/lobehub/lobe-chat/compare/v0.33.0...v0.34.0)

<sup>Released on **2023-07-31**</sup>

#### ✨ Features

- **misc**: Add agent settings functionality, Add new components and features for AgentMeta, Improve organization and functionality of settings and configuration features.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add agent settings functionality ([b0aaeed](https://github.com/lobehub/lobe-chat/commit/b0aaeed))
- **misc**: Add new components and features for AgentMeta ([1232d95](https://github.com/lobehub/lobe-chat/commit/1232d95))
- **misc**: Improve organization and functionality of settings and configuration features ([badde35](https://github.com/lobehub/lobe-chat/commit/badde35))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.33.0](https://github.com/lobehub/lobe-chat/compare/v0.32.0...v0.33.0)

<sup>Released on **2023-07-30**</sup>

#### ✨ Features

- **misc**: 支持输入模板预处理.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持输入模板预处理 ([84082c1](https://github.com/lobehub/lobe-chat/commit/84082c1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.32.0](https://github.com/lobehub/lobe-chat/compare/v0.31.0...v0.32.0)

<sup>Released on **2023-07-30**</sup>

#### ✨ Features

- **misc**: 支持会话置顶.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持会话置顶，closes [#32](https://github.com/lobehub/lobe-chat/issues/32) ([fc44b5d](https://github.com/lobehub/lobe-chat/commit/fc44b5d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.31.0](https://github.com/lobehub/lobe-chat/compare/v0.30.1...v0.31.0)

<sup>Released on **2023-07-30**</sup>

#### ✨ Features

- **misc**: 支持展示 token 使用量.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持展示 token 使用量，closes [#31](https://github.com/lobehub/lobe-chat/issues/31) ([e4d4dac](https://github.com/lobehub/lobe-chat/commit/e4d4dac))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.30.1](https://github.com/lobehub/lobe-chat/compare/v0.30.0...v0.30.1)

<sup>Released on **2023-07-30**</sup>

#### 💄 Styles

- **misc**: 优化搜索引擎插件展示.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 优化搜索引擎插件展示 ([347e6b0](https://github.com/lobehub/lobe-chat/commit/347e6b0))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.30.0](https://github.com/lobehub/lobe-chat/compare/v0.29.0...v0.30.0)

<sup>Released on **2023-07-30**</sup>

#### ✨ Features

- **misc**: 优化保存为话题功能，实现 Topic 重命名功能，实现话题删除功能，支持缓存角色面板的展开折叠状态.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 优化保存为话题功能 ([fdbe084](https://github.com/lobehub/lobe-chat/commit/fdbe084))
- **misc**: 实现 Topic 重命名功能 ([5ef1685](https://github.com/lobehub/lobe-chat/commit/5ef1685))
- **misc**: 实现话题删除功能 ([970889d](https://github.com/lobehub/lobe-chat/commit/970889d))
- **misc**: 支持缓存角色面板的展开折叠状态 ([c241c4b](https://github.com/lobehub/lobe-chat/commit/c241c4b))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.29.0](https://github.com/lobehub/lobe-chat/compare/v0.28.0...v0.29.0)

<sup>Released on **2023-07-30**</sup>

#### ✨ Features

- **misc**: 实现单个会话和角色的导出功能，实现清空所有会话消息.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 实现单个会话和角色的导出功能 ([d15a481](https://github.com/lobehub/lobe-chat/commit/d15a481))
- **misc**: 实现清空所有会话消息 ([64c5125](https://github.com/lobehub/lobe-chat/commit/64c5125))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.28.0](https://github.com/lobehub/lobe-chat/compare/v0.27.4...v0.28.0)

<sup>Released on **2023-07-30**</sup>

#### ♻ Code Refactoring

- **misc**: 重构 settings 相关类型.

#### ✨ Features

- **misc**: 优化 SideBar 实现，激活态指示更加明确，实现 session 导入功能，实现配置导出功能.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 重构 settings 相关类型 ([6b7c0a0](https://github.com/lobehub/lobe-chat/commit/6b7c0a0))

#### What's improved

- **misc**: 优化 SideBar 实现，激活态指示更加明确 ([8a467df](https://github.com/lobehub/lobe-chat/commit/8a467df))
- **misc**: 实现 session 导入功能 ([5650167](https://github.com/lobehub/lobe-chat/commit/5650167))
- **misc**: 实现配置导出功能 ([c1f73fe](https://github.com/lobehub/lobe-chat/commit/c1f73fe))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.27.4](https://github.com/lobehub/lobe-chat/compare/v0.27.3...v0.27.4)

<sup>Released on **2023-07-29**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正日志超过 4096 长度的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正日志超过 4096 长度的问题 ([6066aff](https://github.com/lobehub/lobe-chat/commit/6066aff))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.27.3](https://github.com/lobehub/lobe-chat/compare/v0.27.2...v0.27.3)

<sup>Released on **2023-07-29**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正返回结果导致插件无法正常识别的问题.

#### 💄 Styles

- **misc**: 优化样式.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正返回结果导致插件无法正常识别的问题 ([b183188](https://github.com/lobehub/lobe-chat/commit/b183188))

#### Styles

- **misc**: 优化样式 ([9ce5d1d](https://github.com/lobehub/lobe-chat/commit/9ce5d1d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.27.2](https://github.com/lobehub/lobe-chat/compare/v0.27.1...v0.27.2)

<sup>Released on **2023-07-29**</sup>

#### ♻ Code Refactoring

- **misc**: 重构并优化文档抓取插件能力.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 重构并优化文档抓取插件能力 ([ff56348](https://github.com/lobehub/lobe-chat/commit/ff56348))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.27.1](https://github.com/lobehub/lobe-chat/compare/v0.27.0...v0.27.1)

<sup>Released on **2023-07-29**</sup>

#### 💄 Styles

- **misc**: 优化搜索引擎样式.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 优化搜索引擎样式 ([699afb3](https://github.com/lobehub/lobe-chat/commit/699afb3))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.27.0](https://github.com/lobehub/lobe-chat/compare/v0.26.1...v0.27.0)

<sup>Released on **2023-07-29**</sup>

#### ✨ Features

- **misc**: 优化搜索引擎插件交互展示.

#### 💄 Styles

- **misc**: 优化兜底结果展示.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 优化搜索引擎插件交互展示 ([4751084](https://github.com/lobehub/lobe-chat/commit/4751084))

#### Styles

- **misc**: 优化兜底结果展示 ([9da45d6](https://github.com/lobehub/lobe-chat/commit/9da45d6))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.26.1](https://github.com/lobehub/lobe-chat/compare/v0.26.0...v0.26.1)

<sup>Released on **2023-07-29**</sup>

#### ♻ Code Refactoring

- **misc**: 优化 setting Layout 实现.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 优化 setting Layout 实现 ([f789935](https://github.com/lobehub/lobe-chat/commit/f789935))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.26.0](https://github.com/lobehub/lobe-chat/compare/v0.25.0...v0.26.0)

<sup>Released on **2023-07-28**</sup>

#### ✨ Features

- **misc**: support password auth and error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: support password auth and error, closes [#22](https://github.com/lobehub/lobe-chat/issues/22) ([67f1f4d](https://github.com/lobehub/lobe-chat/commit/67f1f4d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.25.0](https://github.com/lobehub/lobe-chat/compare/v0.24.0...v0.25.0)

<sup>Released on **2023-07-26**</sup>

#### ✨ Features

- **sidebar**: Add import functionality and set labels and onClick functions

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### ✨ Features

- **sidebar**: Add import functionality and set labels and onClick functions ([03ea9bd](https://github.com/lobehub/lobe-chat/commit/03ea9bd))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.24.0](https://github.com/lobehub/lobe-chat/compare/v0.23.0...v0.24.0)

<sup>Released on **2023-07-26**</sup>

#### ✨ Features

- **misc**: Add new translations, update existing translations, add functionality to components, modify styling, and adjust placeholder text

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### ✨ Features

- Add new translations, update existing translations, add functionality to components, modify styling, and adjust placeholder text ([da4ae72](https://github.com/lobehub/lobe-chat/commit/da4ae72))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.23.0](https://github.com/lobehub/lobe-chat/compare/v0.22.2...v0.23.0)

<sup>Released on **2023-07-26**</sup>

#### ✨ Features

- **misc**: Add new features, update URLs, customize appearance, and implement components

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### ✨ Features

- Add new features, update URLs, customize appearance, and implement components ([4b61bf4](https://github.com/lobehub/lobe-chat/commit/4b61bf4))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.22.2](https://github.com/lobehub/lobe-chat/compare/v0.22.1...v0.22.2)

<sup>Released on **2023-07-26**</sup>

#### 💄 Styles

- **misc**: 优化 tooltip 显示.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 优化 tooltip 显示 ([4ba0295](https://github.com/lobehub/lobe-chat/commit/4ba0295))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.22.1](https://github.com/lobehub/lobe-chat/compare/v0.22.0...v0.22.1)

<sup>Released on **2023-07-25**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正自定义 OpenAI API Key 的使用问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正自定义 OpenAI API Key 的使用问题 ([84475c0](https://github.com/lobehub/lobe-chat/commit/84475c0))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.22.0](https://github.com/lobehub/lobe-chat/compare/v0.21.0...v0.22.0)

<sup>Released on **2023-07-25**</sup>

#### ✨ Features

- **misc**: 支持使用自定义 OpenAI Key.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持使用自定义 OpenAI Key, closes [#20](https://github.com/lobehub/lobe-chat/issues/20) ([fb454a0](https://github.com/lobehub/lobe-chat/commit/fb454a0))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.21.0](https://github.com/lobehub/lobe-chat/compare/v0.20.0...v0.21.0)

<sup>Released on **2023-07-25**</sup>

#### ♻ Code Refactoring

- **misc**: Move component folder.

#### ✨ Features

- **misc**: 支持快捷配置模型、温度.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Move component folder ([fb85d16](https://github.com/lobehub/lobe-chat/commit/fb85d16))

#### What's improved

- **misc**: 支持快捷配置模型、温度，closes [#19](https://github.com/lobehub/lobe-chat/issues/19) ([31daee1](https://github.com/lobehub/lobe-chat/commit/31daee1))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.20.0](https://github.com/lobehub/lobe-chat/compare/v0.19.0...v0.20.0)

<sup>Released on **2023-07-25**</sup>

#### ✨ Features

- **misc**: 实现话题模块.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 实现话题模块，closes [#16](https://github.com/lobehub/lobe-chat/issues/16) ([64fd6ee](https://github.com/lobehub/lobe-chat/commit/64fd6ee))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.19.0](https://github.com/lobehub/lobe-chat/compare/v0.18.2...v0.19.0)

<sup>Released on **2023-07-24**</sup>

#### ♻ Code Refactoring

- **misc**: 将 message reducer 提取到独立文件夹中，清理无用代码实现.

#### ✨ Features

- **misc**: 数据结构层完成 topic 模型改造.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 将 message reducer 提取到独立文件夹中 ([64f40ca](https://github.com/lobehub/lobe-chat/commit/64f40ca))
- **misc**: 清理无用代码实现 ([3655b60](https://github.com/lobehub/lobe-chat/commit/3655b60))

#### What's improved

- **misc**: 数据结构层完成 topic 模型改造 ([99fa2a6](https://github.com/lobehub/lobe-chat/commit/99fa2a6))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.18.2](https://github.com/lobehub/lobe-chat/compare/v0.18.1...v0.18.2)

<sup>Released on **2023-07-24**</sup>

#### 💄 Styles

- **misc**: 修正 markdown li 丢失的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 修正 markdown li 丢失的问题 ([eb6e831](https://github.com/lobehub/lobe-chat/commit/eb6e831))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.18.1](https://github.com/lobehub/lobe-chat/compare/v0.18.0...v0.18.1)

<sup>Released on **2023-07-24**</sup>

#### ♻ Code Refactoring

- **misc**: 优化新会话的创建逻辑 session.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 优化新会话的创建逻辑 session ([d70f22d](https://github.com/lobehub/lobe-chat/commit/d70f22d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.18.0](https://github.com/lobehub/lobe-chat/compare/v0.17.0...v0.18.0)

<sup>Released on **2023-07-24**</sup>

#### ✨ Features

- **misc**: 实现会话展示模式切换，并优化默认创建角色的配置.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 实现会话展示模式切换，并优化默认创建角色的配置 ([27ae82f](https://github.com/lobehub/lobe-chat/commit/27ae82f))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.17.0](https://github.com/lobehub/lobe-chat/compare/v0.16.1...v0.17.0)

<sup>Released on **2023-07-24**</sup>

#### ✨ Features

- **misc**: 表单配置支持设定各项高级参数.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 表单配置支持设定各项高级参数 ([6949cc6](https://github.com/lobehub/lobe-chat/commit/6949cc6))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.16.1](https://github.com/lobehub/lobe-chat/compare/v0.16.0...v0.16.1)

<sup>Released on **2023-07-24**</sup>

#### ♻ Code Refactoring

- **misc**: 重构优化 selectors 实现.

#### 💄 Styles

- **misc**: 优化 document title.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 重构优化 selectors 实现 ([97fe1cd](https://github.com/lobehub/lobe-chat/commit/97fe1cd))

#### Styles

- **misc**: 优化 document title ([c3cda00](https://github.com/lobehub/lobe-chat/commit/c3cda00))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.16.0](https://github.com/lobehub/lobe-chat/compare/v0.15.1...v0.16.0)

<sup>Released on **2023-07-24**</sup>

#### ✨ Features

- **misc**: 支持自动跳转到第一条会话.

#### 💄 Styles

- **misc**: 修正插件的展示文案.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持自动跳转到第一条会话 ([54f01c7](https://github.com/lobehub/lobe-chat/commit/54f01c7))

#### Styles

- **misc**: 修正插件的展示文案 ([53c81ae](https://github.com/lobehub/lobe-chat/commit/53c81ae))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.15.1](https://github.com/lobehub/lobe-chat/compare/v0.15.0...v0.15.1)

<sup>Released on **2023-07-24**</sup>

#### 💄 Styles

- **misc**: 更新插件文案.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 更新插件文案 ([0411335](https://github.com/lobehub/lobe-chat/commit/0411335))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.15.0](https://github.com/lobehub/lobe-chat/compare/v0.14.0...v0.15.0)

<sup>Released on **2023-07-24**</sup>

#### ✨ Features

- **misc**: Add new features and improve user experience, Import and use constants from "meta.ts" instead of "agentConfig".

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add new features and improve user experience ([64c8782](https://github.com/lobehub/lobe-chat/commit/64c8782))
- **misc**: Import and use constants from "meta.ts" instead of "agentConfig" ([1eb6a17](https://github.com/lobehub/lobe-chat/commit/1eb6a17))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.14.0](https://github.com/lobehub/lobe-chat/compare/v0.13.1...v0.14.0)

<sup>Released on **2023-07-24**</sup>

#### ✨ Features

- **misc**: 支持网页抓取.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持网页抓取，closes [#14](https://github.com/lobehub/lobe-chat/issues/14) ([9e933b0](https://github.com/lobehub/lobe-chat/commit/9e933b0))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.13.1](https://github.com/lobehub/lobe-chat/compare/v0.13.0...v0.13.1)

<sup>Released on **2023-07-23**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正搜索引擎插件的实现问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正搜索引擎插件的实现问题 ([d19a805](https://github.com/lobehub/lobe-chat/commit/d19a805))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.13.0](https://github.com/lobehub/lobe-chat/compare/v0.12.1...v0.13.0)

<sup>Released on **2023-07-23**</sup>

#### ✨ Features

- **misc**: 优化插件模式下的用户体验.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 优化插件模式下的用户体验，closes [#13](https://github.com/lobehub/lobe-chat/issues/13) ([4596f12](https://github.com/lobehub/lobe-chat/commit/4596f12))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.12.1](https://github.com/lobehub/lobe-chat/compare/v0.12.0...v0.12.1)

<sup>Released on **2023-07-23**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正 message parentId 不正确的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正 message parentId 不正确的问题 ([f86852a](https://github.com/lobehub/lobe-chat/commit/f86852a))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.12.0](https://github.com/lobehub/lobe-chat/compare/v0.11.0...v0.12.0)

<sup>Released on **2023-07-23**</sup>

#### ✨ Features

- **misc**: 支持插件列表 与 基于 Serpapi 的搜索引擎插件.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持插件列表 与 基于 Serpapi 的搜索引擎插件，closes [#12](https://github.com/lobehub/lobe-chat/issues/12) ([d89e06f](https://github.com/lobehub/lobe-chat/commit/d89e06f))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.11.0](https://github.com/lobehub/lobe-chat/compare/v0.10.2...v0.11.0)

<sup>Released on **2023-07-23**</sup>

#### ♻ Code Refactoring

- **misc**: Remove langchain, 优化代码.

#### ✨ Features

- **misc**: 支持查询天气.

#### 💄 Styles

- **misc**: Update manifest, 增加国际化文案.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Remove langchain ([7b0f96c](https://github.com/lobehub/lobe-chat/commit/7b0f96c))
- **misc**: 优化代码 ([6a8f7df](https://github.com/lobehub/lobe-chat/commit/6a8f7df))

#### What's improved

- **misc**: 支持查询天气 ([34bf285](https://github.com/lobehub/lobe-chat/commit/34bf285))

#### Styles

- **misc**: Update manifest ([ea9e8de](https://github.com/lobehub/lobe-chat/commit/ea9e8de))
- **misc**: 增加国际化文案 ([f5e8d7c](https://github.com/lobehub/lobe-chat/commit/f5e8d7c))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.10.2](https://github.com/lobehub/lobe-chat/compare/v0.10.1...v0.10.2)

<sup>Released on **2023-07-23**</sup>

#### 💄 Styles

- **misc**: 优化模型在 list 中的展示逻辑.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 优化模型在 list 中的展示逻辑 ([4bdf3c5](https://github.com/lobehub/lobe-chat/commit/4bdf3c5))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.10.1](https://github.com/lobehub/lobe-chat/compare/v0.10.0...v0.10.1)

<sup>Released on **2023-07-22**</sup>

#### 💄 Styles

- **misc**: 修正对话中用户头像的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 修正对话中用户头像的问题 ([560c8bb](https://github.com/lobehub/lobe-chat/commit/560c8bb))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.10.0](https://github.com/lobehub/lobe-chat/compare/v0.9.0...v0.10.0)

<sup>Released on **2023-07-22**</sup>

#### ✨ Features

- **misc**: 支持复制与编辑会话消息.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持复制与编辑会话消息 ([bebcf9f](https://github.com/lobehub/lobe-chat/commit/bebcf9f))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.9.0](https://github.com/lobehub/lobe-chat/compare/v0.8.2...v0.9.0)

<sup>Released on **2023-07-22**</sup>

#### ✨ Features

- **misc**: 展示模型类型.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 展示模型类型 ([58ea93c](https://github.com/lobehub/lobe-chat/commit/58ea93c))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.8.2](https://github.com/lobehub/lobe-chat/compare/v0.8.1...v0.8.2)

<sup>Released on **2023-07-22**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix miss manifest.json link, 优化 model tag 展示逻辑.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix miss manifest.json link ([ac4b2f3](https://github.com/lobehub/lobe-chat/commit/ac4b2f3))
- **misc**: 优化 model tag 展示逻辑 ([3463ede](https://github.com/lobehub/lobe-chat/commit/3463ede))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.8.1](https://github.com/lobehub/lobe-chat/compare/v0.8.0...v0.8.1)

<sup>Released on **2023-07-22**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix import.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix import ([4fb9967](https://github.com/lobehub/lobe-chat/commit/4fb9967))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.8.0](https://github.com/lobehub/lobe-chat/compare/v0.7.0...v0.8.0)

<sup>Released on **2023-07-22**</sup>

#### ✨ Features

- **misc**: 支持 pwa 模式.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持 pwa 模式 ([8aad92d](https://github.com/lobehub/lobe-chat/commit/8aad92d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.7.0](https://github.com/lobehub/lobe-chat/compare/v0.6.1...v0.7.0)

<sup>Released on **2023-07-22**</sup>

#### ✨ Features

- **misc**: 支持展示来自模型的标记信息.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持展示来自模型的标记信息 ([e27fae9](https://github.com/lobehub/lobe-chat/commit/e27fae9))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.6.1](https://github.com/lobehub/lobe-chat/compare/v0.6.0...v0.6.1)

<sup>Released on **2023-07-22**</sup>

#### 🐛 Bug Fixes

- **misc**: Add deps.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Add deps ([3dc45fe](https://github.com/lobehub/lobe-chat/commit/3dc45fe))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.6.0](https://github.com/lobehub/lobe-chat/compare/v0.5.0...v0.6.0)

<sup>Released on **2023-07-22**</sup>

#### ♻ Code Refactoring

- **misc**: 重构 selector 文件组织.

#### ✨ Features

- **misc**: 补充 token 详情.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 重构 selector 文件组织 ([2ad0ef9](https://github.com/lobehub/lobe-chat/commit/2ad0ef9))

#### What's improved

- **misc**: 补充 token 详情 ([098f7ff](https://github.com/lobehub/lobe-chat/commit/098f7ff))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.5.0](https://github.com/lobehub/lobe-chat/compare/v0.4.3...v0.5.0)

<sup>Released on **2023-07-22**</sup>

#### ✨ Features

- **misc**: 支持选择 Emoji.

#### 🐛 Bug Fixes

- **misc**: 修正 total token 计算不正确的问题.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: 支持选择 Emoji ([6cb4828](https://github.com/lobehub/lobe-chat/commit/6cb4828))

#### What's fixed

- **misc**: 修正 total token 计算不正确的问题 ([17815c6](https://github.com/lobehub/lobe-chat/commit/17815c6))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.4.3](https://github.com/lobehub/lobe-chat/compare/v0.4.2...v0.4.3)

<sup>Released on **2023-07-22**</sup>

#### ♻ Code Refactoring

- **misc**: 优化 edit 代码结构.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: 优化 edit 代码结构 ([fdb3a3f](https://github.com/lobehub/lobe-chat/commit/fdb3a3f))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.4.2](https://github.com/lobehub/lobe-chat/compare/v0.4.1...v0.4.2)

<sup>Released on **2023-07-22**</sup>

#### 💄 Styles

- **misc**: Fix input style, fix layout.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix input style ([504bd64](https://github.com/lobehub/lobe-chat/commit/504bd64))
- **misc**: Fix layout ([2d83aff](https://github.com/lobehub/lobe-chat/commit/2d83aff))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.4.1](https://github.com/lobehub/lobe-chat/compare/v0.4.0...v0.4.1)

<sup>Released on **2023-07-22**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix SSR style error.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix SSR style error ([289eae7](https://github.com/lobehub/lobe-chat/commit/289eae7))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.4.0](https://github.com/lobehub/lobe-chat/compare/v0.3.0...v0.4.0)

<sup>Released on **2023-07-20**</sup>

#### ✨ Features

- **misc**: Add styles and modify layout of FolderPanel, SliderWithInput, SessionList, EditPage, ChatLayout, and SettingLayout components, Introduce FOLDER_WIDTH constant and update components.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add styles and modify layout of FolderPanel, SliderWithInput, SessionList, EditPage, ChatLayout, and SettingLayout components ([7f19a09](https://github.com/lobehub/lobe-chat/commit/7f19a09))
- **misc**: Introduce FOLDER_WIDTH constant and update components ([c511964](https://github.com/lobehub/lobe-chat/commit/c511964))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.3.0](https://github.com/lobehub/lobe-chat/compare/v0.2.0...v0.3.0)

<sup>Released on **2023-07-18**</sup>

#### ✨ Features

- **misc**: Add new files, modify components, and adjust layout and styling.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add new files, modify components, and adjust layout and styling ([b8c3b38](https://github.com/lobehub/lobe-chat/commit/b8c3b38))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

## [Version 0.2.0](https://github.com/lobehub/lobe-chat/compare/v0.1.6...v0.2.0)

<sup>Released on **2023-07-18**</sup>

#### ✨ Features

- **misc**: Add import statement and define CSS styles for Avatar component.

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Add import statement and define CSS styles for Avatar component ([8c23a8d](https://github.com/lobehub/lobe-chat/commit/8c23a8d))

</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>

### [Version 0.1.6](https://github.com/lobehub/lobe-chat/compare/v0.1.5...v0.1.6)

<sup>Released on **2023-07-18**</sup>

<br />

<details>
  <summary><kbd>Improvements and Fixes</kbd></summary>
</details>

<div align="right">
  [![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)
</div>
