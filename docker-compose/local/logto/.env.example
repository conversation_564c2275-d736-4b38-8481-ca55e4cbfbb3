# Logto secret
AUTH_LOGTO_ID=
AUTH_LOGTO_SECRET=
AUTH_LOGTO_ISSUER=

# MinIO S3 configuration
MINIO_ROOT_USER=YOUR_MINIO_USER
MINIO_ROOT_PASSWORD=YOUR_MINIO_PASSWORD

# Configure the bucket information of MinIO
MINIO_LOBE_BUCKET=lobe
S3_ACCESS_KEY_ID=
S3_SECRET_ACCESS_KEY=

# Proxy, if you need it
# HTTP_PROXY=http://localhost:7890
# HTTPS_PROXY=http://localhost:7890


# Other environment variables, as needed. You can refer to the environment variables configuration for the client version, making sure not to have ACCESS_CODE.
# OPENAI_API_KEY=sk-xxxx
# OPENAI_PROXY_URL=https://api.openai.com/v1
# OPENAI_MODEL_LIST=...


# ----- Other config -----
# if no special requirements, no need to change
LOBE_PORT=3210
LOGTO_PORT=3001
MINIO_PORT=9000

# Postgres related, which are the necessary environment variables for DB
LOBE_DB_NAME=lobechat
POSTGRES_PASSWORD=uWNZugjBqixf8dxC
