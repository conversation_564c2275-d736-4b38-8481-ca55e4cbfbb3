name: lobe-chat-database
services:
  network-service:
    image: alpine
    container_name: lobe-network
    ports:
      - '9000:9000' # MinIO API
      - '9001:9001' # Min<PERSON> Console
      - '8080:8080' # Zitadel Console
      - '3210:3210' # LobeChat
    command: tail -f /dev/null
    networks:
      - lobe-network

  postgresql:
    image: pgvector/pgvector:pg16
    container_name: lobe-postgres
    ports:
      - '5432:5432'
    volumes:
      - './data:/var/lib/postgresql/data'
    environment:
      - 'POSTGRES_DB=lobechat'
      - 'POSTGRES_PASSWORD=uWNZugjBqixf8dxC'
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 5s
      timeout: 5s
      retries: 5
    restart: always
    networks:
      - lobe-network

  minio:
    image: minio/minio
    container_name: lobe-minio
    network_mode: 'service:network-service'
    volumes:
      - './s3_data:/etc/minio/data'
    environment:
      - 'MINIO_ROOT_USER=YOUR_MINIO_USER'
      - 'MINIO_ROOT_PASSWORD=YOUR_MINIO_PASSWORD'
      - 'MINIO_API_CORS_ALLOW_ORIGIN=http://localhost:3210'
    restart: always
    command: >
      server /etc/minio/data --address ":9000" --console-address ":9001"


  zitadel:
    restart: 'always'
    image: 'ghcr.io/zitadel/zitadel:latest'
    container_name: lobe-zitadel
    network_mode: 'service:network-service'
    command: start-from-init --config /zitadel-config.yaml --steps /zitadel-init-steps.yaml --masterkey "cft3Tekr/rQBOqwoQSCPoncA9BHbn7QJ" --tlsMode disabled #MasterkeyNeedsToHave32Characters
    volumes:
      - ./zitadel-config.yaml:/zitadel-config.yaml:ro
      - ./zitadel-init-steps.yaml:/zitadel-init-steps.yaml:ro
    depends_on:
      postgresql:
        condition: service_healthy

  lobe:
    image: lobehub/lobe-chat-database
    container_name: lobe-chat
    network_mode: 'service:network-service'
    depends_on:
      postgresql:
        condition: service_healthy
      network-service:
        condition: service_started
      minio:
        condition: service_started
      zitadel:
        condition: service_started
    env_file:
      - .env
    restart: always

volumes:
  data:
    driver: local
  s3_data:
    driver: local
networks:
  lobe-network:
    driver: bridge
