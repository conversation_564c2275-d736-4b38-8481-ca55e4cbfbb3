Log:
  Level: 'info'

Port: 8080
ExternalPort: 8080 
ExternalDomain: localhost
ExternalSecure: false
TLS:
  Enabled: false 

# If not using the docker compose example, adjust these values for connecting ZITADEL to your PostgreSQL
Database:
  postgres:
    Host: postgresql
    Port: 5432
    Database: zitadel
    User:
      Username: 'zitadel'
      Password: 'zitadel'
      SSL:
        Mode: 'disable'
    Admin:
      Username: 'postgres'
      Password: 'uWNZugjBqixf8dxC' #postgres password
      SSL:
        Mode: 'disable'
