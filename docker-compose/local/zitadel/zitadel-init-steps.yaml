# All possible options and their defaults: https://github.com/zitadel/zitadel/blob/main/cmd/setup/steps.yaml
FirstInstance:
  Org:
    Human:
      # use <NAME_EMAIL>
      Username: 'root'
      # The password must be 8 characters or more and must contain uppercase letters, lowercase letters, symbols, and numbers. The first login will require a password change. 
      Password: 'Password1!'
      Email:
        # Optional, if set, can be used to log in with email.
        Address: '<EMAIL>' # ZITADEL_FIRSTINSTANCE_ORG_HUMAN_EMAIL_ADDRESS  
