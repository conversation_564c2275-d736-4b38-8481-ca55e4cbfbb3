name: lobe-chat-database
services:
  postgresql:
    image: pgvector/pgvector:pg16
    container_name: lobe-postgres
    ports:
      - '5432:5432'
    volumes:
      - './data:/var/lib/postgresql/data'
    environment:
      - 'POSTGRES_DB=lobe'
      - 'POSTGRES_PASSWORD=uWNZugjBqixf8dxC'
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 5s
      timeout: 5s
      retries: 5
    restart: always

  minio:
    image: minio/minio
    container_name: lobe-minio
    ports:
      - '9000:9000'
      - '9001:9001'
    volumes:
      - './s3_data:/etc/minio/data'
    environment:
      - 'MINIO_ROOT_USER=YOUR_MINIO_USER'
      - 'MINIO_ROOT_PASSWORD=YOUR_MINIO_PASSWORD'
      - 'MINIO_DOMAIN=lobe-s3-api.example.com'
      - 'MINIO_API_CORS_ALLOW_ORIGIN=https://lobe.example.com' # Your LobeChat's domain name.
    restart: always
    command: >
      server /etc/minio/data --address ":9000" --console-address ":9001"


  logto:
    image: svhd/logto
    container_name: lobe-logto
    ports:
      - '3001:3001'
      - '3002:3002'
    depends_on:
      postgresql:
        condition: service_healthy
    environment:
      - 'TRUST_PROXY_HEADER=1'
      - 'DB_URL=******************************************************/logto'
      - 'ENDPOINT=https://lobe-auth-api.example.com'
      - 'ADMIN_ENDPOINT=https://lobe-auth-ui.example.com'
    entrypoint: ['sh', '-c', 'npm run cli db seed -- --swe && npm start']

  lobe:
    image: lobehub/lobe-chat-database
    container_name: lobe-chat
    ports:
      - '3210:3210'
    depends_on:
      - postgresql
      - minio
      - logto
    env_file:
      - .env
    restart: always

volumes:
  data:
    driver: local
  s3_data:
    driver: local
