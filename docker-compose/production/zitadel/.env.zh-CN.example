# 必填，LobeChat 域名，用于 tRPC 调用
# 请保证此域名在你的 NextAuth 鉴权服务提供商、S3 服务商的 CORS 白名单中
APP_URL=https://lobe.example.com/

# Postgres 相关，也即 DB 必需的环境变量
# 必填，用于加密敏感信息的密钥，可以使用 openssl rand -base64 32 生成
KEY_VAULTS_SECRET=Kix2wcUONd4CX51E/ZPAd36BqM4wzJgKjPtz2sGztqQ=
# 必填，Postgres 数据库连接字符串，用于连接到数据库
# 格式：postgresql://username:password@host:port/dbname，如果你的 pg 实例为 Docker 容器且位于同一 docker-compose 文件中，亦可使用容器名作为 host
DATABASE_URL=******************************************************/lobe

# NEXT_AUTH 相关，也即鉴权服务必需的环境变量
# 必填，NextAuth 的 URL，用于 NextAuth 的回调
NEXTAUTH_URL=https://lobe.example.com/api/auth
# 必填，用于 NextAuth 的密钥，可以使用 openssl rand -base64 32 生成
NEXT_AUTH_SECRET=NX2kaPE923dt6BL2U8e9oSre5RfoT7hg
# 必填，指定鉴权服务提供商
NEXT_AUTH_SSO_PROVIDERS=zitadel

# ZiTADEL 鉴权服务提供商部分
# 请参考：https://lobehub.com/zh/docs/self-hosting/advanced/auth/next-auth/zitadel
AUTH_ZITADEL_ID=285934220675723622
AUTH_ZITADEL_SECRET=pe7Nh3lopXkZkfqh5YEDYI2xsbIz08eZKqInOUZxssd3refRia518Apbv3DZ
AUTH_ZITADEL_ISSUER=https://zitadel.example.com

# S3 相关，也即非结构化数据（文件、图片等）存储必需的环境变量
# 这里以 MinIO 为例
# 必填，S3 的 Access Key ID，对于 MinIO 来说，直到在 MinIO UI 中手动创建之前都是无效的
S3_ACCESS_KEY_ID=YOUR_S3_ACCESS_KEY_ID
# 必填，S3 的 Secret Access Key，对于 MinIO 来说，直到在 MinIO UI 中手动创建之前都是无效的
S3_SECRET_ACCESS_KEY=YOUR_S3_SECRET_ACCESS_KEY
# 必填，S3 的 Endpoint，用于服务端/客户端连接到 S3 API
S3_ENDPOINT=https://lobe-s3-api.example.com
# 必填，S3 的 Bucket，直到在 MinIO UI 中手动创建之前都是无效的
S3_BUCKET=lobe
# 必填，S3 的 Public Domain，用于客户端通过公开连接访问非结构化数据
S3_PUBLIC_DOMAIN=https://lobe-s3-api.example.com
# 选填，S3 的 Enable Path Style
# 对于主流 S3 Cloud 服务商，一般填 0 即可；对于自部署的 MinIO，请填 1
# 请参考：https://lobehub.com/zh/docs/self-hosting/advanced/s3#s-3-enable-path-style
S3_ENABLE_PATH_STYLE=1

# 其他基础环境变量，视需求而定。注意不要有 ACCESS_CODE
# 请参考：https://lobehub.com/zh/docs/self-hosting/environment-variables/basic
# 请注意，对于服务端版本，其 API 必须支持嵌入（即 OpenAI text-embedding-3-small）模型，否则无法对上传文件进行处理，但你无需在 OPENAI_MODEL_LIST 中指定此模型
# OPENAI_API_KEY=sk-xxxx
# OPENAI_PROXY_URL=https://api.openai.com/v1
# OPENAI_MODEL_LIST=...
