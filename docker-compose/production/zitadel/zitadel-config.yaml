Log:
  Level: 'info'

ExternalPort: 443 
ExternalDomain: example.com #Your zitadel's domain name
ExternalSecure: true 
TLS:
  Enabled: false # ZITADEL_TLS_ENABLED

# If not using the docker compose example, adjust these values for connecting ZITADEL to your PostgreSQL
Database:
  postgres:
    Host: postgresql
    Port: 5432
    Database: zitadel
    User:
      Username: 'zitadel'
      Password: 'zitadel'
      SSL:
        Mode: 'disable'
    Admin:
      Username: 'postgres'
      Password: 'uWNZugjBqixf8dxC' #postgres password
      SSL:
        Mode: 'disable'
