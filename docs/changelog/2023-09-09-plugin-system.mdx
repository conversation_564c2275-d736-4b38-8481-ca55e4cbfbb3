---
title: LobeChat Plugin Ecosystem - Functionality Extensions and Development Resources
description: >-
  Discover how the LobeChat plugin ecosystem enhances the utility and flexibility of the LobeChat assistant, along with the development resources and plugin development guidelines provided.

tags:
  - LobeChat
  - Plugins
  - Real-time Information
  - Voice Options
---

# Supported Plugin System

The LobeChat plugin ecosystem is a significant extension of its core functionalities, greatly enhancing the utility and flexibility of the LobeChat assistant.

<Video src="https://github.com/lobehub/lobe-chat/assets/28616219/f29475a3-f346-4196-a435-41a6373ab9e2" />

By leveraging plugins, the LobeChat assistants are capable of accessing and processing real-time information, such as searching online for data and providing users with timely and relevant insights.

Moreover, these plugins are not solely limited to news aggregation; they can also extend to other practical functionalities, such as quickly retrieving documents, generating images, obtaining data from various platforms such as Bilibili and Steam, and interacting with an array of third-party services.

To learn more, please refer to the [Plugin Usage](/en/docs/usage/plugins/basic). Additionally, quality voice options (OpenAI Audio, Microsoft Edge Speech) are available to cater to users from different regions and cultural backgrounds. Users can select suitable voices based on personal preferences or specific situations, providing a personalized communication experience.
