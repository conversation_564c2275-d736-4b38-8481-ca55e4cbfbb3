---
title: LobeChat 支持 Ollama 调用本地大语言模型（LLM）
description: LobeChat vLobeChat v0.127.0 支持 Ollama 调用本地大语言模型。
tags:
  - Ollama AI
  - LobeChat
  - 大语言模型
  - AI 对话
---

# 支持 Ollama 调用本地大语言模型 🦙

随着 LobeChat v0.127.0 的发布，我们迎来了一个激动人心的特性 —— Ollama AI 支持！🤯 在 [Ollama AI](https://ollama.ai/) 强大的基础设施和 [社区的共同努力](https://github.com/lobehub/lobe-chat/pull/1265) 下，现在您可以在 LobeChat 中与本地 LLM (Large Language Model) 进行交流了！🤩

我们非常高兴能在这个特别的时刻，向所有 LobeChat 用户介绍这项革命性的特性。Ollama AI 的集成不仅标志着我们技术上的一个巨大飞跃，更是向用户承诺，我们将不断追求更高效、更智能的沟通方式。

## 💡 如何启动与本地 LLM 的对话？

如果您在私有化部署方面遇到困难，强烈推荐尝试 LobeChat Cloud 服务。我们提供全方位的模型支持，让您轻松开启 AI 对话之旅。

赶快来体验全新升级的 LobeChat v1.6，感受 GPT-4 带来的强大对话能力！

```bash
docker run -d -p 3210:3210 -e OLLAMA_PROXY_URL=http://host.docker.internal:11434/v1 lobehub/lobe-chat
```

是的，就是这么简单！🤩 您不需要进行繁杂的配置，也不必担心复杂的安装过程。我们已经为您准备好了一切，只需一行命令，即可开启与本地 AI 的深度对话。
