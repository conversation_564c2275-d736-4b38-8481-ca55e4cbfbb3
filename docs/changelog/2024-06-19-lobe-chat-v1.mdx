---
title: 'LobeChat 1.0: New Architecture and New Possibilities'
description: >-
  LobeChat 1.0 brings a brand-new architecture and features for server-side databases and user authentication management, opening up new possibilities. On this basis, LobeChat Cloud has entered beta testing.

tags:
  - LobeChat
  - Version 1.0
  - Server-Side Database
  - User Authentication
  - Cloud Beta Testing
---

# LobeChat 1.0: New Architecture and New Possibilities

Since announcing our move towards version 1.0 in March, we’ve been busy upgrading every aspect of our platform. After two months of intensive development, we are excited to announce the official release of LobeChat 1.0! Let’s take a look at our new features.

## Server-Side Database Support

The most significant feature of LobeChat 1.0 is the support for server-side databases. In the 0.x era, the lack of persistent storage on the server side made it challenging, if not impossible, to implement many features that users urgently needed, such as knowledge bases, cross-device synchronization, and private assistant markets.

## User Authentication Management

In the 0.x era, the most requested feature to be paired with server-side databases was user authentication management. Previously, we had integrated next-auth and Clerk as our authentication solutions. In response to demands for multi-user management, we have restructured the settings interface into a user panel, consolidating relevant user information within the new user interface.

## LobeChat Cloud Beta Testing

LobeChat Cloud is our commercial version based on the open-source LobeChat, and all the features from version 1.0 are now live in LobeChat Cloud, which has entered beta testing. If you’re interested, you can join our waitlist here. During the beta testing period, a limited number of access slots will be released daily for testing opportunities.
