---
title: 重磅更新：LobeChat 迎来 Artifacts 时代
description: >-
  LobeChat v1.19 带来了重大更新，包括 Claude Artifacts 完整特性支持、全新的发现页面设计，以及 GitHub Models 服务商支持，让 AI 助手的能力得到显著提升。

tags:
  - LobeChat
  - Artifacts
  - AI 助手
  - 更新
  - GitHub Models
---

# 重磅更新：LobeChat 迎来 Artifacts 时代

我们很高兴地宣布 LobeChat v1.19 版本正式发布！这次更新带来了多项重要功能，让 AI 助手的交互体验更上一层楼。

## 🎨 Artifacts 支持：解锁全新创作维度

在这个版本中，我们几乎完整还原了 Claude Artifacts 的核心特性。现在，您可以在 LobeChat 中体验到：

- SVG 图形生成与展示
- HTML 页面生成与实时渲染
- 更多格式的文档生成

值得一提的是，Python 代码执行功能也已完成开发，将在后续版本中与大家见面。届时，用户将能够同时运用 Claude Artifacts 和 OpenAI Code Interpreter 这两大强大工具，极大提升 AI 助手的实用性。

![Artifacts 功能展示](https://github.com/user-attachments/assets/2787824c-a13c-466c-ba6f-820bddfe099f)

## 🔍 全新发现页面：探索更多可能

发现页面迎来了重大升级，现在包含更丰富的内容类别：

- AI 助手市场
- 插件展示
- 模型列表
- 服务商介绍

这次改版不仅提升了页面的信息密度，更为用户打开了探索 AI 能力的新窗口。未来，我们计划进一步扩展发现页面的功能，可能会加入：

- 知识库分享
- Artifacts 展示
- 精选对话分享

## 🚀 GitHub Models 支持：更多模型选择

感谢社区成员 [@CloudPassenger](https://github.com/CloudPassenger) 的贡献，现在 LobeChat 已经支持 GitHub Models 服务商。用户只需：

1. 准备 GitHub Personal Access Token (PAT)
2. 在设置中配置服务商信息
3. 即可开始使用 GitHub Models 上的免费模型

这一功能的加入大大扩展了用户可选用的模型范围，为不同场景下的 AI 对话提供了更多选择。

## 🔜 未来展望

我们将持续致力于提升 LobeChat 的功能和用户体验。接下来的版本中，我们计划：

- 完善 Python 代码执行功能
- 增加更多 Artifacts 类型支持
- 扩展发现页面的内容维度

感谢每一位用户的支持与反馈，让我们一起期待 LobeChat 带来更多惊喜！
