---
title: LobeChat Supports Sharing Conversations in Text Format (Markdown/JSON)
description: >-
  LobeChat v1.28.0 introduces support for exporting conversations in Markdown and OpenAI format JSON, making it easy to convert conversation content into note materials, development debugging data, and training corpora, significantly enhancing the reusability of conversation content.

tags:
  - Text Format Export
  - Markdown Export
  - OpenAI JSON
---

# Upgraded Conversation Sharing: Support for Text Format Export

In the latest version v1.28.0, we have launched the text format export feature for conversation content, now supporting exports in both Markdown and OpenAI format JSON.

The Markdown export feature meets users' needs for directly using conversation content in note-taking and document writing. You can easily save valuable conversation content and manage it across various note-taking applications for reuse.

![Exporting Conversations as Markdown Text](https://github.com/user-attachments/assets/29508dda-2382-430f-bc81-fb23f02149f8)

Additionally, we support exporting conversations in JSON format that complies with OpenAI messages specifications. This format can be used directly for API debugging and serves as high-quality training data for models.

![Exporting Conversations as <PERSON><PERSON><PERSON> in OpenAI API Specification](https://github.com/user-attachments/assets/484f28f4-017c-4ed7-948b-4a8d51f0b63a)

It is particularly noteworthy that we retain the original data of Tools Calling within the conversation, which is crucial for enhancing the model's tool invocation capabilities.

This update greatly expands the sharing and application scenarios for conversation content, and we hope these new features will enhance your user experience.
