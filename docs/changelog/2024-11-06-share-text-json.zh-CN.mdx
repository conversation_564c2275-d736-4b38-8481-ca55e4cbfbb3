---
title: LobeChat 支持分享对话为文本格式（Markdown/JSON）
description: >-
  LobeChat  v1.28.0 新增 Markdown 和 OpenAI 格式 JSON 导出支持，让对话内容能轻松转化为笔记素材、开发调试数据和训练语料，显著提升对话内容的复用价值。

tags:
  - 对话内容
  - Markdown导出
  - OpenAI JSON
---

# 对话内容分享升级：支持文本格式导出

我们在最新版本 v1.28.0 中推出了对话内容的文本格式导出功能，现在支持将对话内容导出为 Markdown 和 OpenAI 格式的 JSON 两种格式。

Markdown 格式导出功能满足了用户将对话内容直接用于笔记和文档撰写的需求。您可以轻松地将有价值的对话内容保存下来，并在各类笔记软件中进行管理和复用。

![将对话导出为 Markdown 格式文本](https://github.com/user-attachments/assets/29508dda-2382-430f-bc81-fb23f02149f8)

同时，我们还支持将对话导出为符合 OpenAI messages 规范的 JSON 格式。这种格式不仅可以直接用于 API 调试，还能作为高质量的模型训练语料。

![将对话导出为 OpenAI 接口规范的 JSON](https://github.com/user-attachments/assets/484f28f4-017c-4ed7-948b-4a8d51f0b63a)

特别值得一提的是，我们会完整保留对话中的 Tools Calling 原始数据，这对提升模型的工具调用能力具有重要价值。

这次更新让对话内容的分享和应用场景得到了极大扩展，期待这些新功能能够提升您的使用体验。
